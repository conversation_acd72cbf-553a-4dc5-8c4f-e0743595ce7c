#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导出模块
负责将分析结果导出为Excel和JSON格式
"""

import json
import logging
import pandas as pd
from typing import List, Dict, Any
from tkinter import messagebox

class DataExporter:
    """数据导出器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def export_to_excel(self, results: List[Dict[str, Any]], filename: str) -> bool:
        """导出Excel文件"""
        try:
            if not results:
                messagebox.showwarning("警告", "没有分析结果可导出！")
                return False
            
            # 准备导出数据
            export_data = []
            for result in results:
                row = {
                    "产品名称": result.get("产品名称", ""),
                    "文件名": result.get("文件名", ""),
                    "蓝海指数": result.get("蓝海指数", 0),
                    "评级": result.get("评级", ""),
                    "搜索人气": result.get("搜索人气", ""),
                    "搜索人气增长": result.get("搜索人气增长", "-"),
                    "支付转化率": result.get("支付转化率", ""),
                    "转化率增长": result.get("转化率增长", "-"),
                    "支付买家数": result.get("支付买家数", ""),
                    "买家数增长": result.get("买家数增长", "-"),
                    "需求供给比": result.get("需求供给比", 0),
                    "需求供给比增长": result.get("需求供给比增长", "-"),
                    "天猫占比": result.get("天猫占比", ""),
                    "天猫占比增长": result.get("天猫占比增长", "-"),
                }
                
                # 添加否决原因（如果有）
                if "否决原因" in result:
                    row["否决原因"] = result["否决原因"]
                
                # 添加分析详情
                if "分析详情" in result and isinstance(result["分析详情"], list):
                    row["分析详情"] = "; ".join(result["分析详情"])
                
                export_data.append(row)
            
            # 创建DataFrame并导出
            df = pd.DataFrame(export_data)
            
            # 按蓝海指数降序排列
            df = df.sort_values('蓝海指数', ascending=False)
            
            # 导出到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='蓝海分析结果', index=False)
                
                # 获取工作表对象进行格式化
                worksheet = writer.sheets['蓝海分析结果']
                
                # 调整列宽
                column_widths = {
                    'A': 30,  # 产品名称
                    'B': 15,  # 文件名
                    'C': 12,  # 蓝海指数
                    'D': 20,  # 评级
                    'E': 15,  # 搜索人气
                    'F': 12,  # 搜索人气增长
                    'G': 15,  # 支付转化率
                    'H': 12,  # 转化率增长
                    'I': 15,  # 支付买家数
                    'J': 12,  # 买家数增长
                    'K': 12,  # 需求供给比
                    'L': 15,  # 需求供给比增长
                    'M': 12,  # 天猫占比
                    'N': 12,  # 天猫占比增长
                    'O': 30,  # 否决原因
                    'P': 50,  # 分析详情
                }
                
                for col, width in column_widths.items():
                    worksheet.column_dimensions[col].width = width
            
            messagebox.showinfo("导出成功", f"Excel文件已保存到: {filename}")
            self.logger.info(f"Excel导出成功: {filename}")
            return True
            
        except Exception as e:
            error_msg = f"导出Excel失败: {str(e)}"
            messagebox.showerror("导出失败", error_msg)
            self.logger.error(error_msg)
            return False
    
    def export_to_json(self, results: List[Dict[str, Any]], filename: str) -> bool:
        """导出JSON文件"""
        try:
            if not results:
                messagebox.showwarning("警告", "没有分析结果可导出！")
                return False
            
            # 按蓝海指数降序排列
            sorted_results = sorted(results, key=lambda x: x.get('蓝海指数', 0), reverse=True)
            
            # 准备导出数据
            export_data = {
                "export_info": {
                    "total_products": len(sorted_results),
                    "export_time": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "version": "1.0"
                },
                "results": sorted_results
            }
            
            # 导出到JSON
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("导出成功", f"JSON文件已保存到: {filename}")
            self.logger.info(f"JSON导出成功: {filename}")
            return True
            
        except Exception as e:
            error_msg = f"导出JSON失败: {str(e)}"
            messagebox.showerror("导出失败", error_msg)
            self.logger.error(error_msg)
            return False
    
    def generate_summary_report(self, results: List[Dict[str, Any]]) -> str:
        """生成分析摘要报告"""
        if not results:
            return "没有分析结果"
        
        # 统计数据
        total_count = len(results)
        
        # 统计被一票否决的产品
        vetoed_results = [r for r in results if "否决原因" in r]
        vetoed_count = len(vetoed_results)
        
        # 统计通过筛选的产品
        passed_results = [r for r in results if "否决原因" not in r]
        passed_count = len(passed_results)
        
        # 计算平均分（仅统计通过筛选的产品）
        if passed_results:
            avg_score = sum(r.get('蓝海指数', 0) for r in passed_results) / len(passed_results)
            best_product = max(passed_results, key=lambda x: x.get('蓝海指数', 0))
            max_score = best_product.get('蓝海指数', 0)
        else:
            avg_score = 0
            best_product = {"产品名称": "无"}
            max_score = 0
        
        # 统计评级分布
        ratings = {}
        for result in results:
            rating = result.get("评级", "未知")
            ratings[rating] = ratings.get(rating, 0) + 1
        
        # 生成报告
        summary = f"""📊 蓝海产品分析摘要报告

总产品数: {total_count}
🚫 一票否决: {vetoed_count}个
✅ 通过筛选: {passed_count}个
平均蓝海指数: {avg_score:.1f} (仅统计通过筛选的产品)

产品评级分布:
"""
        
        for rating, count in ratings.items():
            percentage = (count / total_count) * 100
            summary += f"• {rating}: {count}个 ({percentage:.1f}%)\n"
        
        summary += f"""
🏆 最佳产品: {best_product['产品名称']}
📈 最高指数: {max_score}

💡 左侧显示识别顺序，右侧显示评分排序
"""
        
        return summary
    
    def get_export_statistics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取导出统计信息"""
        if not results:
            return {}
        
        # 基本统计
        total_count = len(results)
        vetoed_count = len([r for r in results if "否决原因" in r])
        passed_count = total_count - vetoed_count
        
        # 评级统计
        rating_stats = {}
        for result in results:
            rating = result.get("评级", "未知")
            rating_stats[rating] = rating_stats.get(rating, 0) + 1
        
        # 分数统计
        scores = [r.get('蓝海指数', 0) for r in results if "否决原因" not in r]
        score_stats = {
            "max_score": max(scores) if scores else 0,
            "min_score": min(scores) if scores else 0,
            "avg_score": sum(scores) / len(scores) if scores else 0
        }
        
        return {
            "total_count": total_count,
            "vetoed_count": vetoed_count,
            "passed_count": passed_count,
            "rating_distribution": rating_stats,
            "score_statistics": score_stats
        }
