#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蓝海产品分析器 v2.0 启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """主函数"""
    try:
        # 导入主程序
        from blue_ocean_analyzer_v2 import BlueOceanAnalyzerV2

        print("🚀 启动蓝海产品分析器 v2.0...")

        # 创建并启动分析器
        app = BlueOceanAnalyzerV2()
        app.root.mainloop()

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有必要的文件都在当前目录中")
        input("按回车键退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
