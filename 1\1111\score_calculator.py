#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评分计算模块
负责蓝海指数计算和一票否决权检查
"""

import re
import logging
from typing import Dict, Any, Tuple, List

class ScoreCalculator:
    """评分计算器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
    
    def extract_min_from_range(self, range_str: str) -> int:
        """从范围字符串中提取最小值"""
        if not range_str or range_str == '-':
            return 0
        
        # 处理数字范围，如 "2500 ~ 5000", "1万 ~ 2万"
        range_str = str(range_str).replace('万', '0000')
        
        # 提取数字
        numbers = re.findall(r'\d+', range_str)
        if numbers:
            return int(numbers[0])
        return 0
    
    def extract_percentage(self, percent_str: str) -> float:
        """从百分比字符串中提取数值"""
        if isinstance(percent_str, str):
            # 移除百分号并提取数字
            clean_str = percent_str.replace('%', '')
            # 提取转化率范围的最大值 (如 "10% - 15%" 取15)
            numbers = re.findall(r'(\d+\.?\d*)', clean_str)
            if numbers:
                return max(float(num) for num in numbers)
        return 0.0
    
    def extract_max_rate(self, rate_str: str) -> int:
        """从转化率字符串中提取最大值"""
        if isinstance(rate_str, str):
            numbers = re.findall(r'\d+', rate_str)
            if numbers:
                return max(int(num) for num in numbers)
        return 0
    
    def extract_growth_rate(self, growth_str: str) -> float:
        """从增长率字符串中提取数值"""
        if not growth_str or growth_str == '-':
            return 0.0
        
        # 移除符号和百分号
        clean_str = str(growth_str).replace('+', '').replace('%', '').replace(',', '')
        
        # 提取数字
        numbers = re.findall(r'(\d+\.?\d*)', clean_str)
        if numbers:
            return float(numbers[0])
        return 0.0
    
    def get_current_config_value(self, key: str, default_value: float) -> float:
        """获取当前配置值"""
        filter_config = self.config_manager.get_filter_config()
        return filter_config.get(key, default_value)
    
    def check_veto_conditions(self, product_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        检查一票否决权条件
        返回: (is_vetoed, veto_reasons)
        """
        veto_reasons = []
        
        # 获取当前配置的阈值
        min_popularity_threshold = self.get_current_config_value("否决_搜索人气最小值", 500)
        min_buyers_threshold = self.get_current_config_value("否决_支付买家数最小值", 10)
        min_supply_demand_threshold = self.get_current_config_value("否决_需求供给比最小值", 0.5)
        max_tmall_threshold = self.get_current_config_value("否决_天猫占比最大值", 70.0)
        
        # 1. 搜索人气检查
        search_popularity = product_data.get('搜索人气', '')
        min_popularity = self.extract_min_from_range(search_popularity)
        if min_popularity < min_popularity_threshold:
            veto_reasons.append(f"搜索人气过低({min_popularity} < {min_popularity_threshold})")
        
        # 2. 支付买家数检查
        buyer_count = product_data.get('支付买家数', '')
        min_buyers = self.extract_min_from_range(buyer_count)
        if min_buyers < min_buyers_threshold:
            veto_reasons.append(f"支付买家数过低({min_buyers} < {min_buyers_threshold})")
        
        # 3. 需求供给比检查
        supply_demand_ratio = product_data.get('需求供给比', 0)
        if supply_demand_ratio < min_supply_demand_threshold:
            veto_reasons.append(f"需求供给比过低({supply_demand_ratio} < {min_supply_demand_threshold})")
        
        # 4. 天猫占比检查
        tmall_ratio = product_data.get('天猫占比', '')
        tmall_percentage = self.extract_percentage(tmall_ratio)
        if tmall_percentage > max_tmall_threshold:
            veto_reasons.append(f"天猫占比过高({tmall_percentage}% > {max_tmall_threshold}%)")
        
        is_vetoed = len(veto_reasons) > 0
        return is_vetoed, veto_reasons
    
    def calculate_blue_ocean_score(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算蓝海指数 - 添加一票否决权功能"""
        # 🚫 首先检查一票否决权
        is_vetoed, veto_reasons = self.check_veto_conditions(product_data)
        if is_vetoed:
            # 被否决直接标记为红海产品
            return {
                **product_data,
                "蓝海指数": 0,
                "评级": "🚫 红海产品(一票否决)",
                "否决原因": '; '.join(veto_reasons),
                "分析详情": [f"❌ {reason}" for reason in veto_reasons]
            }
        
        # 通过一票否决权检查，进行正常评分
        base_score = 0
        analysis_details = ["✅ 通过一票否决权检查"]

        # 获取配置
        filter_config = self.config_manager.get_filter_config()
        scoring_config = self.config_manager.get_scoring_config()

        # 需求供给比评分
        supply_demand_ratio = product_data.get("需求供给比", 0)
        supply_config = scoring_config["基础评分"]["需求供给比"]

        if supply_demand_ratio >= filter_config.get("需求供给比_优秀", 5.0):
            base_score += supply_config["优秀_分数"]
            analysis_details.append(f"🔥 需求供给比优秀: {supply_demand_ratio} (+{supply_config['优秀_分数']}分)")
        elif supply_demand_ratio >= filter_config.get("需求供给比_良好", 2.0):
            base_score += supply_config["良好_分数"]
            analysis_details.append(f"👍 需求供给比良好: {supply_demand_ratio} (+{supply_config['良好_分数']}分)")
        elif supply_demand_ratio >= supply_config["一般_阈值"]:
            base_score += supply_config["一般_分数"]
            analysis_details.append(f"⚠️ 需求供给比一般: {supply_demand_ratio} (+{supply_config['一般_分数']}分)")
        else:
            analysis_details.append(f"❌ 需求供给比较低: {supply_demand_ratio} (+0分)")
        
        # 转化率评分
        conversion_rate = self.extract_max_rate(product_data.get("支付转化率", "0%"))
        conversion_config = scoring_config["基础评分"]["转化率"]

        if conversion_rate >= filter_config.get("转化率_优秀", 40.0):
            base_score += conversion_config["优秀_分数"]
            analysis_details.append(f"💰 转化率优秀: {conversion_rate}% (+{conversion_config['优秀_分数']}分)")
        elif conversion_rate >= filter_config.get("转化率_良好", 25.0):
            base_score += conversion_config["良好_分数"]
            analysis_details.append(f"💵 转化率良好: {conversion_rate}% (+{conversion_config['良好_分数']}分)")
        elif conversion_rate >= conversion_config["一般_阈值"]:
            base_score += conversion_config["一般_分数"]
            analysis_details.append(f"💸 转化率一般: {conversion_rate}% (+{conversion_config['一般_分数']}分)")
        else:
            analysis_details.append(f"❌ 转化率较低: {conversion_rate}% (+0分)")
        
        # 天猫占比评分 - 占比越低越好
        tmall_percentage = self.extract_percentage(product_data.get("天猫占比", "100%"))
        tmall_config = scoring_config["基础评分"]["天猫占比"]

        if tmall_percentage <= filter_config.get("天猫占比_低", 50.0):
            base_score += tmall_config["低_分数"]
            analysis_details.append(f"🎯 天猫占比低: {tmall_percentage}% (+{tmall_config['低_分数']}分)")
        elif tmall_percentage <= filter_config.get("天猫占比_中", 80.0):
            base_score += tmall_config["中_分数"]
            analysis_details.append(f"⚖️ 天猫占比中等: {tmall_percentage}% (+{tmall_config['中_分数']}分)")
        else:
            base_score += tmall_config["高_分数"]
            analysis_details.append(f"⚠️ 天猫占比较高: {tmall_percentage}% (+{tmall_config['高_分数']}分)")
        
        # 搜索人气基础分
        search_popularity = self.extract_min_from_range(product_data.get("搜索人气", "0"))
        popularity_config = scoring_config["基础评分"]["搜索人气"]

        if search_popularity >= popularity_config["很高_阈值"]:
            base_score += popularity_config["很高_分数"]
            analysis_details.append(f"🔥 搜索人气很高: {search_popularity} (+{popularity_config['很高_分数']}分)")
        elif search_popularity >= popularity_config["较高_阈值"]:
            base_score += popularity_config["较高_分数"]
            analysis_details.append(f"📈 搜索人气较高: {search_popularity} (+{popularity_config['较高_分数']}分)")
        elif search_popularity >= popularity_config["中等_阈值"]:
            base_score += popularity_config["中等_分数"]
            analysis_details.append(f"📊 搜索人气中等: {search_popularity} (+{popularity_config['中等_分数']}分)")
        else:
            base_score += popularity_config["较低_分数"]
            analysis_details.append(f"📉 搜索人气较低: {search_popularity} (+{popularity_config['较低_分数']}分)")
        
        # 趋势加分
        trend_bonus = 0
        trend_config = scoring_config["趋势加分"]

        # 需求供给比增长加分
        supply_growth = self.extract_growth_rate(product_data.get("需求供给比增长", "0%"))
        supply_growth_config = trend_config["需求供给比增长"]

        if supply_growth > supply_growth_config["暴增_阈值"]:
            trend_bonus += supply_growth_config["暴增_分数"]
            analysis_details.append(f"🚀 需求供给比暴增{supply_growth}% (+{supply_growth_config['暴增_分数']}分)")
        elif supply_growth > supply_growth_config["大增_阈值"]:
            trend_bonus += supply_growth_config["大增_分数"]
            analysis_details.append(f"📈 需求供给比大增{supply_growth}% (+{supply_growth_config['大增_分数']}分)")
        elif supply_growth > supply_growth_config["增长_阈值"]:
            trend_bonus += supply_growth_config["增长_分数"]
            analysis_details.append(f"📊 需求供给比增长{supply_growth}% (+{supply_growth_config['增长_分数']}分)")
        
        # 搜索人气增长加分
        popularity_growth = self.extract_growth_rate(product_data.get("搜索人气增长", "0%"))
        popularity_growth_config = trend_config["搜索人气增长"]

        if popularity_growth > popularity_growth_config["暴增_阈值"]:
            trend_bonus += popularity_growth_config["暴增_分数"]
            analysis_details.append(f"🔥 搜索人气暴增{popularity_growth}% (+{popularity_growth_config['暴增_分数']}分)")
        elif popularity_growth > popularity_growth_config["增长_阈值"]:
            trend_bonus += popularity_growth_config["增长_分数"]
            analysis_details.append(f"📈 搜索人气增长{popularity_growth}% (+{popularity_growth_config['增长_分数']}分)")
        elif popularity_growth > popularity_growth_config["小幅增长_阈值"]:
            trend_bonus += popularity_growth_config["小幅增长_分数"]
            analysis_details.append(f"📊 搜索人气小幅增长{popularity_growth}% (+{popularity_growth_config['小幅增长_分数']}分)")
        
        # 买家数增长加分
        buyer_growth = self.extract_growth_rate(product_data.get("买家数增长", "0%"))
        buyer_growth_config = trend_config["买家数增长"]

        if buyer_growth > buyer_growth_config["暴增_阈值"]:
            trend_bonus += buyer_growth_config["暴增_分数"]
            analysis_details.append(f"👥 买家数暴增{buyer_growth}% (+{buyer_growth_config['暴增_分数']}分)")
        elif buyer_growth > buyer_growth_config["增长_阈值"]:
            trend_bonus += buyer_growth_config["增长_分数"]
            analysis_details.append(f"👥 买家数增长{buyer_growth}% (+{buyer_growth_config['增长_分数']}分)")
        
        # 确保总分不超过100分
        final_score = min(base_score + trend_bonus, 100)
        
        # 确定评级
        rating_config = scoring_config["评级标准"]
        if final_score >= rating_config["蓝海产品_最低分"]:
            rating = "🥇 蓝海产品"
        elif final_score >= rating_config["潜力产品_最低分"]:
            rating = "🥈 潜力产品"
        elif final_score >= rating_config["一般产品_最低分"]:
            rating = "🥉 一般产品"
        else:
            rating = "🔴 红海产品"
        
        return {
            **product_data,
            "蓝海指数": final_score,
            "评级": rating,
            "分析详情": analysis_details
        }
