{"filter_config": {"需求供给比_优秀": 5.0, "需求供给比_良好": 2.0, "转化率_优秀": 40.0, "转化率_良好": 25.0, "天猫占比_低": 50.0, "天猫占比_中": 80.0, "否决_搜索人气最小值": 500, "否决_支付买家数最小值": 10, "否决_需求供给比最小值": 0.5, "否决_天猫占比最大值": 70.0}, "scoring_config": {"基础评分": {"需求供给比": {"优秀_分数": 40, "良好_分数": 25, "一般_分数": 10, "一般_阈值": 1.0}, "转化率": {"优秀_分数": 30, "良好_分数": 20, "一般_分数": 10, "一般_阈值": 10.0}, "天猫占比": {"低_分数": 20, "中_分数": 10, "高_分数": 0}, "搜索人气": {"很高_分数": 10, "很高_阈值": 10000, "较高_分数": 7, "较高_阈值": 5000, "中等_分数": 5, "中等_阈值": 2500, "较低_分数": 2}}, "趋势加分": {"需求供给比增长": {"暴增_分数": 8, "暴增_阈值": 1000, "大增_分数": 5, "大增_阈值": 100, "增长_分数": 2, "增长_阈值": 20}, "搜索人气增长": {"暴增_分数": 5, "暴增_阈值": 100, "增长_分数": 3, "增长_阈值": 20, "小幅增长_分数": 1, "小幅增长_阈值": 5}, "买家数增长": {"暴增_分数": 2, "暴增_阈值": 200, "增长_分数": 1, "增长_阈值": 50}}, "评级标准": {"蓝海产品_最低分": 85, "潜力产品_最低分": 70, "一般产品_最低分": 55}}, "ocr_config": {"use_gpu": false, "use_angle_cls": true, "lang": "ch"}, "ui_config": {"window_width": 1200, "window_height": 850, "progress_update_interval": 5}, "logging_config": {"level": "INFO", "log_file": "analyzer.log"}}