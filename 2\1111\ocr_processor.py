#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR处理模块
负责OCR识别和数据提取
"""

import os
import re
import logging
from typing import List, Dict, Any, Optional

class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        self.ocr = None
    
    def init_ocr(self):
        """延迟初始化OCR"""
        if self.ocr is None:
            try:
                from paddleocr import PaddleOCR
                ocr_config = self.config_manager.get_ocr_config()
                self.ocr = PaddleOCR(
                    use_angle_cls=ocr_config.get('use_angle_cls', True),
                    lang=ocr_config.get('lang', 'ch')
                )
                self.logger.info("OCR初始化成功")
            except Exception as e:
                self.logger.error(f"OCR初始化失败: {e}")
                raise
    
    def safe_float_convert(self, value: str) -> float:
        """安全的浮点数转换"""
        if not value or value == '-':
            return 0.0
        
        # 移除逗号和其他非数字字符（保留小数点）
        clean_value = re.sub(r'[^\d.]', '', str(value))
        
        try:
            return float(clean_value)
        except (ValueError, TypeError):
            return 0.0
    
    def standardize_range_format(self, range_str: str) -> str:
        """标准化范围格式"""
        if not range_str:
            return range_str
        
        # 统一使用 " ~ " 作为分隔符
        standardized = str(range_str).replace('~', ' ~ ').replace('-', ' ~ ')
        # 移除多余的空格
        standardized = re.sub(r'\s+', ' ', standardized).strip()
        
        return standardized
    
    # 移除OCR错误修复代码 - PaddleOCR 3.1精度足够高，不需要修复
    
    def group_texts_by_rows(self, text_items: List[Dict]) -> List[List[str]]:
        """将文本按行分组"""
        # 按y坐标排序
        sorted_items = sorted(text_items, key=lambda x: x['y'])

        rows = []
        current_row = []
        current_y = None
        y_threshold = 35  # 增加y坐标差异阈值，更好地处理行分组
        
        for item in sorted_items:
            if current_y is None or abs(item['y'] - current_y) <= y_threshold:
                current_row.append(item)
                current_y = item['y'] if current_y is None else (current_y + item['y']) / 2
            else:
                if current_row:
                    # 按x坐标排序当前行
                    current_row.sort(key=lambda x: x['x'])

                    # 如果是11列数据行，需要特殊处理最后两列（天猫占比列）
                    if len(current_row) == 11:
                        # 最后两列是垂直排列的，需要按y坐标重新排序
                        last_two = current_row[-2:]  # 获取最后两列
                        other_cols = current_row[:-2]  # 获取前9列

                        # 对最后两列按y坐标排序（y小的在上面，应该是天猫占比）
                        last_two.sort(key=lambda x: x['y'])

                        # 重新组合
                        current_row = other_cols + last_two

                        row_with_coords = [(item['text'], item['x'], item['y']) for item in current_row]
                        print(f"   调试 - 第{len(rows)+2}行最后两列坐标(已修正): {row_with_coords[-2:]} (天猫占比和增长率)")

                    rows.append([item['text'] for item in current_row])
                current_row = [item]
                current_y = item['y']
        
        # 添加最后一行
        if current_row:
            current_row.sort(key=lambda x: x['x'])

            # 如果是11列数据行，需要特殊处理最后两列（天猫占比列）
            if len(current_row) == 11:
                # 最后两列是垂直排列的，需要按y坐标重新排序
                last_two = current_row[-2:]  # 获取最后两列
                other_cols = current_row[:-2]  # 获取前9列

                # 对最后两列按y坐标排序（y小的在上面，应该是天猫占比）
                last_two.sort(key=lambda x: x['y'])

                # 重新组合
                current_row = other_cols + last_two

                row_with_coords = [(item['text'], item['x'], item['y']) for item in current_row]
                print(f"   调试 - 第{len(rows)+2}行最后两列坐标(已修正): {row_with_coords[-2:]} (天猫占比和增长率)")

            rows.append([item['text'] for item in current_row])

        return rows

    def fix_ocr_errors(self, text: str) -> str:
        """修复常见的OCR识别错误"""
        # OCR常见错误修正映射
        ocr_fixes = {
            '% L ~ %': '5% ~ 7.5%',  # 常见的OCR错误
            '%S-': '-5%',            # 另一个常见错误
            '万~': '万 ~ ',          # 格式标准化
            '2万~4万': '2万 ~ 4万',   # 格式标准化
        }

        fixed_text = text
        for error, correction in ocr_fixes.items():
            fixed_text = fixed_text.replace(error, correction)

        return fixed_text

    def is_valid_product_name(self, text: str) -> bool:
        """判断是否为有效的产品名称"""
        if not text or len(text.strip()) < 2:  # 降低长度要求
            return False

        # 排除表头关键词
        header_keywords = [
            '相关搜索词', '相关搜素词', '搜索人气', '搜素人气', '支付转化率', '支付买家数',
            '需求供给比', '天猫商品点击占比', '天猫占比', '天猫商品点击占比○',
            '转化率', '买家数', '供给比', '点击占比', '产品名称', '产品分析'
        ]

        text_clean = text.strip()
        for keyword in header_keywords:
            if keyword in text_clean:
                return False

        # 特殊处理：如果包含产品关键词，直接认为是有效产品名
        product_keywords = [
            '防雨', '围裙', '雨衣', '防风', '过夜', '专业', '透明', '防水',
            '加厚', '妇女', '家用', '手洗', '衣服', '下雨天', '专用',
            '主妇', '成年', '电动车', '手套', '头盔', '方雨', '小头盔'
        ]
        for keyword in product_keywords:
            if keyword in text_clean:
                return True

        # 排除纯数字、百分比、符号等
        if re.match(r'^[\d\s%~\-+.,万]+$', text_clean):
            return False

        # 排除数字范围格式（如：4万~8万，1000~2500）- 但要更严格，避免误杀产品名
        if re.match(r'^\d+[万千]?\s*[~\-]\s*\d+[万千]?\s*$', text_clean):
            return False

        # 如果包含中文字符且长度合理，认为是产品名
        if re.search(r'[\u4e00-\u9fff]', text_clean) and len(text_clean) >= 2:
            # 额外检查：如果主要是数字范围但包含少量中文，仍然排除
            if re.match(r'^\d+[万千]?.*[~\-].*\d+[万千]?', text_clean):
                return False
            return True

        return False
    
    def extract_product_data(self, ocr_result: List, image_path: str) -> List[Dict[str, Any]]:
        """从OCR结果中提取产品数据"""
        if not ocr_result or not ocr_result[0]:
            return []

        print(f"\n=== 开始处理图片: {os.path.basename(image_path)} ===")

        # 提取所有文本和位置信息
        text_items = []
        for item in ocr_result[0]:
            if len(item) >= 2 and isinstance(item[1], tuple):
                text, confidence = item[1]
                if confidence > 0.6:
                    bbox = item[0]  # 边界框坐标
                    # 计算文本的y坐标（用于按行排序）
                    y_center = (bbox[0][1] + bbox[2][1]) / 2
                    text_items.append({
                        'text': text.strip(),
                        'y': y_center,
                        'x': bbox[0][0],  # 左上角x坐标
                        'confidence': confidence
                    })
                    print(f"OCR识别: '{text.strip()}' (置信度: {confidence:.2f})")

        print(f"\nOCR识别到 {len(text_items)} 个文本项")

        # 按行分组
        rows = self.group_texts_by_rows(text_items)

        print(f"\n识别到 {len(rows)} 行数据:")
        for i, row in enumerate(rows, 1):
            print(f"第{i}行 ({len(row)}列): {row}")

        # 提取产品数据
        products = []

        for i, row in enumerate(rows, 1):
            print(f"\n--- 处理第{i}行 ---")

            if not row:
                continue

            product_name_candidate = row[0].strip()
            print(f"产品名称候选: '{product_name_candidate}'")

            # 检查是否为有效产品名称
            if not self.is_valid_product_name(product_name_candidate):
                print(f"❌ 跳过: {product_name_candidate} (表头或无效)")
                continue

            print(f"✅ 有效产品: {product_name_candidate}")
            print(f"   完整行数据: {row}")

            # 创建产品数据字典
            product = {
                "产品名称": product_name_candidate,
                "文件名": os.path.basename(image_path)
            }

            # 修复OCR错误
            fixed_row = [self.fix_ocr_errors(cell) for cell in row]
            if fixed_row != row:
                print(f"   🔧 OCR修正后: {fixed_row}")
                row = fixed_row

            # 按实际列位置解析数据（支持11列和6列格式）
            if len(row) >= 11:  # 11列格式：包含趋势数据
                try:
                    # 第2列：搜索人气（标准化格式）
                    product["搜索人气"] = self.standardize_range_format(row[1])
                    # 第3列：搜索人气增长率
                    product["搜索人气增长"] = row[2]

                    # 第4列：支付转化率（标准化格式）
                    product["支付转化率"] = self.standardize_range_format(row[3])
                    # 第5列：转化率增长率
                    product["转化率增长"] = row[4]

                    # 第6列：支付买家数（标准化格式）
                    product["支付买家数"] = self.standardize_range_format(row[5])
                    # 第7列：买家数增长率
                    product["买家数增长"] = row[6]

                    # 第9列：需求供给比 (修正：这里是实际的需求供给比数值)
                    product["需求供给比"] = self.safe_float_convert(row[8])
                    # 第8列：需求供给比增长率
                    product["需求供给比增长"] = row[7]

                    # 第10列：天猫占比
                    product["天猫占比"] = row[9]
                    # 第11列：天猫占比增长率
                    product["天猫占比增长"] = row[10]

                    print(f"   ✅ 11列数据映射: 需求供给比={row[8]}, 需求供给比增长={row[7]}")

                except (ValueError, IndexError) as e:
                    self.logger.warning(f"11列数据转换错误: {e}, 行数据: {row}")
                    continue

            elif len(row) >= 6:  # 6列格式：无趋势数据
                try:
                    # 第2列：搜索人气
                    product["搜索人气"] = self.standardize_range_format(row[1])
                    # 第3列：支付转化率
                    product["支付转化率"] = self.standardize_range_format(row[2])
                    # 第4列：支付买家数
                    product["支付买家数"] = self.standardize_range_format(row[3])
                    # 第5列：需求供给比
                    product["需求供给比"] = self.safe_float_convert(row[4])
                    # 第6列：天猫占比
                    product["天猫占比"] = row[5]

                    # 无趋势数据时设置默认值
                    product["搜索人气增长"] = "-"
                    product["转化率增长"] = "-"
                    product["买家数增长"] = "-"
                    product["需求供给比增长"] = "-"
                    product["天猫占比增长"] = "-"

                    print(f"   ✅ 6列数据映射: 需求供给比={row[4]}, 无趋势数据")

                except (ValueError, IndexError) as e:
                    self.logger.warning(f"6列数据转换错误: {e}, 行数据: {row}")
                    continue
            else:
                print(f"   ❌ 跳过: 列数不足 ({len(row)} < 6)")
                continue

            print(f"✅ 最终产品数据: {product}")
            products.append(product)

        print(f"\n=== 最终识别到 {len(products)} 个有效产品 ===")
        return products
    
    def process_image(self, image_path: str) -> List[Dict[str, Any]]:
        """处理单张图片"""
        try:
            if self.ocr is None:
                self.init_ocr()

            # OCR识别
            result = self.ocr.ocr(image_path)

            # 提取数据
            products_data = self.extract_product_data(result, image_path)

            return products_data

        except Exception as e:
            self.logger.error(f"处理图片失败 {image_path}: {e}")
            return []
