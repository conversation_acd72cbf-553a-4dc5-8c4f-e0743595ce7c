@echo off
chcp 65001 >nul
title 蓝海产品分析器 v2.0

echo.
echo ========================================
echo   🌊 蓝海产品分析器 v2.0
echo   基于PaddleOCR的产品分析工具
echo ========================================
echo.

:: 切换到脚本所在目录
cd /d %~dp0

:: 检查并激活lanhai虚拟环境
echo 🔍 检查lanhai虚拟环境...
if exist "C:\Users\<USER>\lanhai\Scripts\activate.bat" (
    echo ✅ 找到lanhai环境，正在激活...
    call "C:\Users\<USER>\lanhai\Scripts\activate.bat"
    echo 🚀 lanhai环境已激活
) else (
    echo ❌ 未找到lanhai虚拟环境！
    echo 💡 请先运行环境安装脚本:
    echo    setup_lanhai_env.bat
    goto :error_exit
)

echo.
echo 🔍 检查Python和依赖...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未找到！
    goto :error_exit
)

echo 🔍 检查PaddleOCR...
python -c "import paddleocr; print('✅ PaddleOCR版本:', paddleocr.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo ❌ PaddleOCR未安装或版本不正确
    echo 💡 请运行: setup_lanhai_env.bat
    goto :error_exit
)

echo 🔍 检查其他依赖...
python -c "import pandas, openpyxl, PIL, numpy, cv2; print('✅ 所有依赖正常')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ 部分依赖缺失
    echo 💡 请运行: setup_lanhai_env.bat
    goto :error_exit
)

echo.
echo 🎯 选择运行模式:
echo    1. 启动蓝海产品分析器 (GUI界面)
echo    2. 环境诊断
echo    3. 测试PaddleOCR效果
echo    4. 退出
echo.

set /p choice=请选择 (1-4): 

if "%choice%"=="1" goto :start_main
if "%choice%"=="2" goto :diagnose
if "%choice%"=="3" goto :test_ocr
if "%choice%"=="4" goto :exit
echo 无效选择，默认启动主程序
goto :start_main

:start_main
echo.
echo 🚀 启动蓝海产品分析器 v2.0...
echo 📊 基于PaddleOCR的产品分析工具
echo.
python main.py
goto :end

:diagnose
echo.
echo 🔍 运行环境诊断...
python -c "
import sys
print('🔍 蓝海产品分析器环境诊断')
print('=' * 40)
print(f'🐍 Python版本: {sys.version}')

if 'lanhai' in sys.prefix:
    print('✅ 运行在lanhai虚拟环境中')
else:
    print('⚠️  未在lanhai虚拟环境中')

packages = {
    'paddleocr': 'PaddleOCR',
    'pandas': 'Pandas', 
    'openpyxl': 'OpenPyXL',
    'PIL': 'Pillow',
    'numpy': 'NumPy',
    'cv2': 'OpenCV'
}

for pkg, name in packages.items():
    try:
        module = __import__(pkg)
        version = getattr(module, '__version__', 'unknown')
        print(f'✅ {name}: {version}')
    except ImportError:
        print(f'❌ {name}: 未安装')

try:
    import paddleocr
    print('🚀 测试PaddleOCR初始化...')
    ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch')
    print('✅ PaddleOCR初始化成功')
    print('✅ OCR引擎就绪')
except Exception as e:
    print(f'❌ PaddleOCR初始化失败: {e}')

print('=' * 40)
print('🎉 诊断完成！')
"
goto :end

:test_ocr
echo.
echo 🧪 测试PaddleOCR识别效果...
echo 💡 这将创建测试图片并进行识别测试
python -c "
import paddleocr
from PIL import Image, ImageDraw
import os

print('📸 创建测试图片...')
img = Image.new('RGB', (400, 200), color='white')
draw = ImageDraw.Draw(img)

test_texts = [
    '蓝海产品分析器',
    'PaddleOCR测试',
    '数字测试: 123456789',
    '文本识别测试'
]

y_pos = 20
for text in test_texts:
    draw.text((20, y_pos), text, fill='black')
    y_pos += 40

test_img = 'test_ocr_lanhai.png'
img.save(test_img)
print(f'✅ 测试图片已保存: {test_img}')

print('🚀 使用PaddleOCR进行识别...')
ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch')
result = ocr.ocr(test_img)

if result and result[0]:
    print(f'✅ 识别到文本:')
    for i, line in enumerate(result[0], 1):
        if len(line) >= 2 and isinstance(line[1], tuple):
            text, confidence = line[1]
            print(f'  {i}. \"{text}\" (置信度: {confidence:.3f})')

    # 计算平均置信度
    confidences = []
    for line in result[0]:
        if len(line) >= 2 and isinstance(line[1], tuple):
            _, confidence = line[1]
            confidences.append(confidence)

    if confidences:
        avg_confidence = sum(confidences) / len(confidences)
        print(f'📊 平均置信度: {avg_confidence:.3f}')

        if avg_confidence > 0.9:
            print('🎉 识别效果优秀！')
        elif avg_confidence > 0.8:
            print('✅ 识别效果良好')
        else:
            print('⚠️  识别效果一般')
    else:
        print('❌ 未能提取置信度')
else:
    print('❌ 未识别到任何文本')

# 清理测试图片
try:
    os.remove(test_img)
    print('🧹 测试图片已清理')
except:
    pass

print('🎊 测试完成！')
"
goto :end

:error_exit
echo.
echo ========================================
echo   ❌ 启动失败
echo   💡 请检查环境配置后重试
echo ========================================
echo.
echo 按任意键退出...
pause >nul
goto :exit

:end
echo.
echo ========================================
echo   程序已退出
echo ========================================
echo.
echo 💡 提示: lanhai虚拟环境将在窗口关闭时自动退出
echo 按任意键退出...
pause >nul

:exit
