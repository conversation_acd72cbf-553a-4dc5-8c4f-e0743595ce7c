o

    _��h�  �                   @   s>   d Z ddlZddlZddlZddlmZmZ G dd� d�ZdS )uE   
配置管理模块
负责加载、保存和管理应用程序配置
�    N)�Dict�Anyc                   @   s>  e Zd ZdZd&defdd�Zdeeef fdd�Zdeee	f fd	d
�Z
deee	f fdd�Zd'deee	f defdd�Z
deeef fdd�Zdeee	f fdd�Zdeee	f fdd�Zdeee	f fdd�Zdeee	f fdd�Zdeeef fdd�Zd'dedefd d!�Zdedefd"d#�Zdeee	f fd$d%�Zd
S )(�
ConfigManageru   配置管理器�config.json�config_pathc                 C   s    || _ t�t�| _| �� | _d S �N)r   �logging�	getLogger�__name__�logger�load_config�_config)�selfr   � r   �JF:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\1111\config_manager.py�__init__   s   zConfigManager.__init__�returnc                 C   s   ddddddddd	d
d�
S )u   获取默认筛选配置g      @g       @g      D@g      9@g      4@g      I@i�  �
   g      �?g     �Q@)
u   需求供给比_优秀u   需求供给比_良好u   转化率_优秀u   转化率_良好u   天猫占比_低u   天猫占比_中u   否决_搜索人气最小值u   否决_支付买家数最小值u   否决_需求供给比最小值u   否决_天猫占比最大值r   �r   r   r   r   �get_default_filter_config   s   �z'ConfigManager.get_default_filter_configc                 C   s(   | � � dddd�dddd�d	d
d�d�S )
u   获取默认完整配置FT�ch�Zuse_gpuZ
use_angle_clsZlang�  �   �   ��window_width�
window_heightZprogress_update_interval�INFO�analyzer.log��level�log_file)�
filter_config�
ocr_config�	ui_config�logging_config)r   r   r   r   r   �get_default_config%   s   ����z ConfigManager.get_default_configc              
   C   s�   | � � }zNtj�| j�rPt| jddd��
}t�|�}W d  � n1 s$w   Y  |�� }|�	� D ]\}}||v rHt
|| t�rH|| �|� q1|||< q1|W S W |S  t
yn } z| j�d|� �� W Y d}~|S d}~ww )u   加载配置文件�r�utf-8��encodingNu/   加载配置文件失败，使用默认配置: )r'   �os�path�existsr   �open�json�load�copy�items�
isinstance�dict�update�	Exceptionr   �warning)r   Zdefault_config�fZconfig_dataZ
merged_config�key�value�er   r   r   r   9   s&   �
����zConfigManager.load_configN�configc              
   C   s�   z/|p| j }t| jddd��}tj||ddd� W d  � n1 s"w   Y  | j�d� W d	S  tyK } z| j�d
|� �� W Y d}~dS d}~ww )u   保存配置到文件�wr)   r*   F�   )Zensure_ascii�indentNu   配置已保存到文件Tu   保存配置失败: )	r
   r/   r   r0   �dumpr   �infor7   �error)r   r=   Zconfig_to_saver9   r<   r   r   r   �save_configP   s   
���zConfigManager.save_configc                 C   �   | j �d| �� �S )u   获取筛选配置r#   )r
   �getr   r   r   r   r   �get_filter_config\   �   zConfigManager.get_filter_configc                 C   rE   )u   获取评分配置Zscoring_config)r
   rF   �get_default_scoring_configr   r   r   r   �get_scoring_config`   rH   z ConfigManager.get_scoring_configc                 C   �   | j �ddddd��S )u   获取OCR配置r$   FTr   r   �r
   rF   r   r   r   r   �get_ocr_configd   �
   �zConfigManager.get_ocr_configc                 C   rK   )u   获取UI配置r%   r   r   r   r   rL   r   r   r   r   �
get_ui_configl   rN   zConfigManager.get_ui_configc                 C   s   | j �dddd��S )u   获取日志配置r&   r   r   r    rL   r   r   r   r   �get_logging_configt   s   �z ConfigManager.get_logging_config�
new_configc                 C   s   | j d �|� dS )u   更新筛选配置r#   N)r
   r6   )r   rQ   r   r   r   �update_filter_config{   s   z"ConfigManager.update_filter_config�sectionr:   c                 C   s   | j �|i ��||�S )u   获取特定配置值rL   )r   rS   r:   �defaultr   r   r   �get_config_value   s   zConfigManager.get_config_valuec                 C   s&   || j vr
i | j |< || j | |< dS )u   设置特定配置值N)r
   )r   rS   r:   r;   r   r   r   �set_config_value�   s   

zConfigManager.set_config_valuec                 C   sx   ddddd�ddddd�ddd	d
�dddd
dddd�d�ddddddd�ddddddd�ddddd�d�ddd d!�d"�S )#u   获取默认评分配置�(   �   r   g      �?)u
   优秀_分数u
   良好_分数u
   一般_分数u
   一般_阈值�   �   g      $@r   )u
   低_分数u
   中_分数u
   高_分数i'  �   i�  r   i�	  �   )u
   很高_分数u
   很高_阈值u
   较高_分数u
   较高_阈值u
   中等_分数u
   中等_阈值u
   较低_分数)u   需求供给比u	   转化率u   天猫占比u   搜索人气�   i�  �d   )�
   暴增_分数�
   暴增_阈值u
   大增_分数u
   大增_阈值�
   增长_分数�
   增长_阈值�   �   )r_   r`   ra   rb   u   小幅增长_分数u   小幅增长_阈值��   �2   )r_   r`   ra   rb   )u   需求供给比增长u   搜索人气增长u   买家数增长�U   �F   �7   )u   蓝海产品_最低分u   潜力产品_最低分u   一般产品_最低分)u   基础评分u   趋势加分u   评级标准r   r   r   r   r   rI   �   s`   ������	�	����z(ConfigManager.get_default_scoring_config)r   r   )r
   �
__module__�__qualname__�__doc__�strr   r   �floatr   r   r'   r   �boolrD   rG   rJ   rM   rO   rP   rR   rU   rV   rI   r   r   r   r   r   
   s     r   )rl   r,   r0   r   �typingr   r   r   r   r   r   r   �<module>   s   