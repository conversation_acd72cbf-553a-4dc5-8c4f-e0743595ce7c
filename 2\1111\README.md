# 🌊 蓝海产品分析器 v2.0

一个基于PaddleOCR技术的抖音电商蓝海产品分析工具，能够识别和分析产品数据，计算蓝海指数。

## ✨ 功能特点

- 🚀 **OCR识别**: 使用PaddleOCR进行文本识别
- 📊 **智能蓝海分析**: 基于多维度数据计算产品蓝海指数
- 📈 **数据可视化**: 直观展示分析结果和趋势
- 💾 **Excel导出**: 支持完整数据导出，格式规范
- 🔄 **批量处理**: 高效处理大量产品截图
- ⚙️ **智能配置**: 自动优化的参数配置

## 🎉 v2.0版本功能

### 🔥 核心功能
- **✅ OCR文本识别**: 使用PaddleOCR进行图像文字识别
- **✅ 数据提取**: 从产品截图中提取关键数据
- **✅ 蓝海分析**: 计算产品蓝海指数
- **✅ 智能修复**: OCR错误自动修复功能
- **✅ 批量处理**: 支持批量图片处理

### 🛠️ 技术特点
- **稳定API**: 使用成熟的OCR接口
- **智能修复**: 自动修复常见OCR识别错误
- **灵活配置**: 支持多种参数配置
- **兼容性好**: 支持多种数据格式

## 🔧 环境要求

### 推荐环境配置
```bash
# 激活lanhai环境 (推荐)
cd C:\Users\<USER>\lanhai && Scripts\activate

# 进入项目目录
cd F:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\1111
```

### 依赖包版本
- **Python**: 3.8+ (推荐3.10)
- **PaddleOCR**: 2.0+ (推荐最新版本)
- **PaddlePaddle**: 最新版本
- **其他**: tkinter, pandas, openpyxl, pillow

## 📦 安装说明

### 快速启动 (推荐)
```bash
# 1. 激活lanhai环境
cd C:\Users\<USER>\lanhai && Scripts\activate

# 2. 进入项目目录
cd F:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\1111

# 3. 运行程序
python main.py
```

### 手动安装依赖
```bash
pip install paddlepaddle paddleocr pandas openpyxl pillow
```

## 🚀 使用方法

### 快速开始
1. **启动程序**: `python main.py`
2. **选择图片**: 点击"选择图片文件夹"
3. **开始分析**: 点击"开始分析"，等待OCR引擎处理
4. **查看结果**: 实时查看识别进度和蓝海指数计算结果
5. **导出数据**: 导出Excel报告

## 📊 功能特性

- **OCR识别**: 基于PaddleOCR的文本识别
- **数据提取**: 智能提取产品关键信息
- **蓝海分析**: 多维度蓝海指数计算
- **批量处理**: 支持大量图片批量分析

## 🏗️ 项目结构

```
蓝海产品分析器 v2.0
├── main.py                    # 🚀 程序入口
├── blue_ocean_analyzer_v2.py  # 📊 主分析器
├── ocr_processor.py           # 🔍 OCR处理模块
├── score_calculator.py        # 📈 蓝海指数计算
├── data_exporter.py           # 📤 数据导出功能
├── data_importer.py           # 📥 数据导入功能
├── config_manager.py          # ⚙️ 配置管理
├── 配置编辑器.py              # 🔧 配置编辑工具
├── config.json                # ⚙️ 配置文件
├── analyzer.log               # 📋 运行日志
└── README.md                  # 📖 项目文档
```

## 📁 文件说明

### 核心文件
- **main.py**: 程序主入口
- **blue_ocean_analyzer_v2.py**: 主分析器逻辑
- **ocr_processor.py**: OCR文本识别处理
- **score_calculator.py**: 蓝海指数计算算法
- **config_manager.py**: 配置文件管理

### 功能模块
- **data_exporter.py**: 数据导出到Excel
- **data_importer.py**: 数据导入功能
- **配置编辑器.py**: 图形化配置编辑工具

## 🔍 核心模块说明

### OCR处理模块 (ocr_processor.py)
- **PaddleOCR集成**: 使用PaddleOCR进行文本识别
- **智能数据映射**: 自动识别产品数据
- **错误修复**: 智能修复OCR识别错误

### 蓝海指数计算 (score_calculator.py)
- **多维度评估**: 搜索人气、转化率、买家数等
- **智能权重**: 自动调整各指标权重
- **趋势分析**: 支持增长率数据分析

## 🐛 常见问题

### Q: 程序启动慢？
A: 首次运行会下载OCR模型文件，请耐心等待。

### Q: 识别精度不高？
A: 可以调整OCR配置参数，提高识别精度。

### Q: 如何提高处理速度？
A: 可以考虑使用GPU加速（如果有NVIDIA显卡）。

## 🔄 更新日志

### v2.0 - 当前版本
- 🚀 **OCR文本识别**: 基于PaddleOCR的文本识别
- 📊 **蓝海分析**: 多维度产品蓝海指数计算
- 🛠️ **智能修复**: OCR错误自动修复功能
- 📤 **数据导出**: 支持Excel格式导出
- ⚙️ **配置管理**: 灵活的参数配置系统

## 📞 技术支持

如有问题或建议，请联系开发者。

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

---

**🎉 恭喜！你现在拥有了功能完整的蓝海产品分析工具！**
