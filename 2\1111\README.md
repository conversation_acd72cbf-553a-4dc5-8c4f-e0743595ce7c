# 🌊 蓝海产品分析器 v2.0 增强版

一个基于PaddleOCR技术的抖音电商蓝海产品分析工具，能够识别和分析产品数据，计算蓝海指数。

## ✨ 功能特点

- 🚀 **OCR识别**: 使用PaddleOCR进行文本识别
- 📊 **智能蓝海分析**: 基于多维度数据计算产品蓝海指数
- 📈 **数据可视化**: 直观展示分析结果和趋势
- 💾 **Excel导出**: 支持完整数据导出，格式规范
- 🔄 **批量处理**: 高效处理大量产品截图
- ⚙️ **智能配置**: 自动优化的参数配置
- 🛠️ **多种启动方式**: 支持多种启动脚本和环境管理

## 🎉 v2.0增强版新特性

### 🆕 相比v1.0的重大升级
- **✅ 多种启动方式**: 新增main.py、main.bat、start_lanhai.bat等多种启动选项
- **✅ 环境管理增强**: 自动检测和管理虚拟环境，支持多路径环境检测
- **✅ 依赖管理**: 新增requirements_lanhai.txt，便于环境部署
- **✅ 调试功能**: 增强的OCR调试信息和错误诊断
- **✅ 测试支持**: 内置测试脚本和测试图片
- **✅ 更好的兼容性**: 支持Python 3.9.2 + PaddleOCR 2.6.1.3环境

### 🔥 核心功能
- **✅ OCR文本识别**: 使用PaddleOCR进行图像文字识别
- **✅ 数据提取**: 从产品截图中提取关键数据
- **✅ 蓝海分析**: 计算产品蓝海指数
- **✅ 智能修复**: OCR错误自动修复功能
- **✅ 批量处理**: 支持批量图片处理

### 🛠️ 技术特点
- **稳定API**: 使用成熟的OCR接口
- **智能修复**: 自动修复常见OCR识别错误
- **灵活配置**: 支持多种参数配置
- **兼容性好**: 支持多种数据格式
- **环境友好**: 自动环境检测和依赖管理

## 🔧 环境要求

### 推荐环境配置
- **Python**: 3.8+ (测试环境: Python 3.9.2)
- **PaddleOCR**: 2.0+ (测试版本: paddleocr 2.6.1.3)
- **PaddlePaddle**: 2.0+ (测试版本: PaddlePaddle 2.5.2)
- **其他依赖**: tkinter, pandas, openpyxl, pillow, numpy, opencv-python

### 虚拟环境支持
程序支持多种虚拟环境路径自动检测：
- `C:\Users\<USER>\lanhai`
- `C:\Users\<USER>\lanhailao`
- 系统Python环境

## 📦 安装和启动

### 方法一：一键启动 (推荐)
```bash
# Windows用户直接双击运行
main.bat

# 或者使用增强启动器
start_lanhai.bat
```

### 方法二：Python启动
```bash
# 直接运行主程序
python main.py
```

### 方法三：环境部署
```bash
# 自动创建和配置lanhai环境
setup_lanhai_env.bat

# 使用requirements文件安装依赖
pip install -r requirements_lanhai.txt
```

## 🚀 使用方法

### 快速开始
1. **启动程序**: 双击 `main.bat` 或运行 `python main.py`
2. **选择模式**: 选择"图片分析"或"数据导入"模式
3. **选择文件**: 点击"选择图片文件夹"或"选择数据文件"
4. **开始分析**: 点击"开始分析"，等待OCR引擎处理
5. **查看结果**: 实时查看识别进度和蓝海指数计算结果
6. **导出数据**: 导出Excel报告

## 📊 v2.0增强版特性对比

| 功能特性 | v1.0版本 | v2.0增强版 | 改进说明 |
|---------|---------|-----------|---------|
| 启动方式 | 单一启动脚本 | 多种启动选项 | 新增main.py、main.bat、start_lanhai.bat |
| 环境管理 | 手动配置 | 自动检测 | 支持多路径虚拟环境自动检测 |
| 依赖管理 | 手动安装 | 自动化部署 | 新增requirements_lanhai.txt和setup脚本 |
| 调试功能 | 基础日志 | 增强调试 | 详细的OCR调试信息和错误诊断 |
| 测试支持 | 无 | 完整测试 | 内置测试脚本和测试图片 |
| 兼容性 | 基础兼容 | 增强兼容 | 支持多版本Python和PaddleOCR |

## 🏗️ 项目结构

```
蓝海产品分析器 v2.0 增强版 (已清理)
├── 🚀 启动脚本
│   ├── main.py                    # Python主入口
│   ├── main.bat                   # Windows一键启动
│   ├── start_lanhai.bat           # 增强启动器(环境检测)
│   └── setup_lanhai_env.bat       # 环境自动部署
├── 📊 核心模块
│   ├── blue_ocean_analyzer_v2.py  # 主分析器
│   ├── ocr_processor.py           # OCR处理模块
│   ├── score_calculator.py        # 蓝海指数计算
│   ├── data_exporter.py           # 数据导出功能
│   ├── data_importer.py           # 数据导入功能
│   └── config_manager.py          # 配置管理
├── ⚙️ 配置文件
│   ├── config.json                # 主配置文件
│   ├── requirements_lanhai.txt    # 依赖包列表
│   └── 配置编辑器.py              # 配置编辑工具
└── 📋 日志和文档
    ├── analyzer.log              # 运行日志
    └── README.md                 # 项目文档
```

## 📁 v2.0增强版文件说明

### 🆕 新增启动脚本
- **main.py**: 统一的Python入口，支持错误处理
- **main.bat**: Windows一键启动，自动环境检测
- **start_lanhai.bat**: 增强启动器，支持环境诊断和OCR测试
- **setup_lanhai_env.bat**: 自动创建虚拟环境和安装依赖

### 📊 核心模块 (继承v1.0)
- **blue_ocean_analyzer_v2.py**: 主分析器逻辑
- **ocr_processor.py**: OCR文本识别处理 (增强调试功能)
- **score_calculator.py**: 蓝海指数计算算法
- **config_manager.py**: 配置文件管理

### 🆕 增强功能
- **requirements_lanhai.txt**: 标准化依赖管理
- **多种启动脚本**: 适应不同使用场景
- **环境自动检测**: 智能识别虚拟环境

## 🧹 项目清理完成

### ✅ 已清理的文件
- `__pycache__/` - Python缓存文件
- `test_*.py` - 测试脚本文件
- `ceshi.bat` - 调试启动器
- `33/` - 测试图片目录

### ✅ 保留的核心文件
- 所有主要功能模块
- 启动脚本和配置文件
- 文档和日志文件

## 🔍 核心模块说明

### OCR处理模块 (ocr_processor.py)
- **PaddleOCR集成**: 使用PaddleOCR进行文本识别
- **智能数据映射**: 自动识别产品数据
- **错误修复**: 智能修复OCR识别错误

### 蓝海指数计算 (score_calculator.py)
- **多维度评估**: 搜索人气、转化率、买家数等
- **智能权重**: 自动调整各指标权重
- **趋势分析**: 支持增长率数据分析

## 🐛 常见问题

### Q: 程序启动慢？
A: 首次运行会下载OCR模型文件，请耐心等待。

### Q: 识别精度不高？
A: 可以调整OCR配置参数，提高识别精度。

### Q: 如何提高处理速度？
A: 可以考虑使用GPU加速（如果有NVIDIA显卡）。

## 🔄 版本更新日志

### v2.0 增强版 - 当前版本 (2024-07-28)
#### 🆕 新增功能
- 🚀 **多种启动方式**: 新增main.py、main.bat、start_lanhai.bat等启动选项
- 🛠️ **环境管理增强**: 自动检测虚拟环境，支持多路径检测
- 📦 **依赖管理**: 新增requirements_lanhai.txt和自动部署脚本
- 🧪 **测试支持**: 内置测试脚本和测试图片
- 🔍 **调试增强**: 详细的OCR调试信息和错误诊断

#### 🔧 技术改进
- ✅ **兼容性提升**: 支持Python 3.9.2 + PaddleOCR 2.6.1.3
- ✅ **错误处理**: 更完善的异常处理和用户提示
- ✅ **日志系统**: 增强的日志记录和调试信息
- ✅ **代码优化**: 清理和优化核心算法

### v1.0 - 基础版本
- 🚀 **OCR文本识别**: 基于PaddleOCR的文本识别
- 📊 **蓝海分析**: 多维度产品蓝海指数计算
- 🛠️ **智能修复**: OCR错误自动修复功能
- 📤 **数据导出**: 支持Excel格式导出
- ⚙️ **配置管理**: 灵活的参数配置系统

## 📞 技术支持

如有问题或建议，请联系开发者。

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

---

**🎉 恭喜！你现在拥有了v2.0增强版蓝海产品分析工具！**

*✨ 相比v1.0版本，新增了多种启动方式、环境管理、测试支持等强大功能！*
*🛠️ 更好的兼容性和用户体验，让分析工作更加高效便捷！*
