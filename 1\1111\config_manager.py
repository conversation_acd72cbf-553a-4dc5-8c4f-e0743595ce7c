#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责加载、保存和管理应用程序配置
"""

import os
import json
import logging
from typing import Dict, Any

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        self._config = self.load_config()
    
    def get_default_filter_config(self) -> Dict[str, float]:
        """获取默认筛选配置"""
        return {
            "需求供给比_优秀": 5.0,
            "需求供给比_良好": 2.0,
            "转化率_优秀": 40.0,
            "转化率_良好": 25.0,
            "天猫占比_低": 50.0,
            "天猫占比_中": 80.0,
            # 一票否决权阈值
            "否决_搜索人气最小值": 500,
            "否决_支付买家数最小值": 10,
            "否决_需求供给比最小值": 0.5,
            "否决_天猫占比最大值": 70.0
        }
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认完整配置"""
        return {
            "filter_config": self.get_default_filter_config(),
            "ocr_config": {
                "use_gpu": False,
                "use_angle_cls": True,
                "lang": "ch"
            },
            "ui_config": {
                "window_width": 1200,
                "window_height": 800,
                "progress_update_interval": 5
            },
            "logging_config": {
                "level": "INFO",
                "log_file": "analyzer.log"
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = self.get_default_config()
        
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    
                # 合并默认配置和加载的配置
                merged_config = default_config.copy()
                for key, value in config_data.items():
                    if key in merged_config and isinstance(merged_config[key], dict):
                        merged_config[key].update(value)
                    else:
                        merged_config[key] = value
                        
                return merged_config
        except Exception as e:
            self.logger.warning(f"加载配置文件失败，使用默认配置: {e}")
        
        return default_config
    
    def save_config(self, config: Dict[str, Any] = None) -> bool:
        """保存配置到文件"""
        try:
            config_to_save = config or self._config
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, ensure_ascii=False, indent=4)
            self.logger.info("配置已保存到文件")
            return True
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
    
    def get_filter_config(self) -> Dict[str, float]:
        """获取筛选配置"""
        return self._config.get('filter_config', self.get_default_filter_config())

    def get_scoring_config(self) -> Dict[str, Any]:
        """获取评分配置"""
        return self._config.get('scoring_config', self.get_default_scoring_config())

    def get_ocr_config(self) -> Dict[str, Any]:
        """获取OCR配置"""
        return self._config.get('ocr_config', {
            "use_gpu": False,
            "use_angle_cls": True,
            "lang": "ch"
        })
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI配置"""
        return self._config.get('ui_config', {
            "window_width": 1200,
            "window_height": 800,
            "progress_update_interval": 5
        })
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self._config.get('logging_config', {
            "level": "INFO",
            "log_file": "analyzer.log"
        })
    
    def update_filter_config(self, new_config: Dict[str, float]):
        """更新筛选配置"""
        self._config['filter_config'].update(new_config)
    
    def get_config_value(self, section: str, key: str, default=None):
        """获取特定配置值"""
        return self._config.get(section, {}).get(key, default)
    
    def set_config_value(self, section: str, key: str, value):
        """设置特定配置值"""
        if section not in self._config:
            self._config[section] = {}
        self._config[section][key] = value

    def get_default_scoring_config(self) -> Dict[str, Any]:
        """获取默认评分配置"""
        return {
            "基础评分": {
                "需求供给比": {
                    "优秀_分数": 40,
                    "良好_分数": 25,
                    "一般_分数": 10,
                    "一般_阈值": 1.0
                },
                "转化率": {
                    "优秀_分数": 30,
                    "良好_分数": 20,
                    "一般_分数": 10,
                    "一般_阈值": 10.0
                },
                "天猫占比": {
                    "低_分数": 20,
                    "中_分数": 10,
                    "高_分数": 0
                },
                "搜索人气": {
                    "很高_分数": 10,
                    "很高_阈值": 10000,
                    "较高_分数": 7,
                    "较高_阈值": 5000,
                    "中等_分数": 5,
                    "中等_阈值": 2500,
                    "较低_分数": 2
                }
            },
            "趋势加分": {
                "需求供给比增长": {
                    "暴增_分数": 8,
                    "暴增_阈值": 1000,
                    "大增_分数": 5,
                    "大增_阈值": 100,
                    "增长_分数": 2,
                    "增长_阈值": 20
                },
                "搜索人气增长": {
                    "暴增_分数": 5,
                    "暴增_阈值": 100,
                    "增长_分数": 3,
                    "增长_阈值": 20,
                    "小幅增长_分数": 1,
                    "小幅增长_阈值": 5
                },
                "买家数增长": {
                    "暴增_分数": 2,
                    "暴增_阈值": 200,
                    "增长_分数": 1,
                    "增长_阈值": 50
                }
            },
            "评级标准": {
                "蓝海产品_最低分": 85,
                "潜力产品_最低分": 70,
                "一般产品_最低分": 55
            }
        }
