o

    ��h��  �                   @   s�   d Z ddlZddlZddlmZmZmZ ddlZddlZddl	m	Z	 ddl
mZ ddlm
Z
 ddlmZ ddlmZ dd	lmZ G d
d� d�ZedkrTe� Ze��  dS dS )
u�   
蓝海产品分析器 v2.4 - 智能文件命名版本
基于OCR的电商数据分析工具，支持批量图片分析，可调节筛选条件，支持筛选导出，智能文件命名
�    N)�ttk�
filedialog�
messagebox)�datetime)�
ConfigManager)�OCRProcessor)�ScoreCalculator)�DataExporter)�DataImporterc                   @   s  e Zd ZdZdd� Zdd� Zdd� Zdd	� Zd
d� Zdd
� Z	dd� Z
defdd�Zdd� Z
dd� Zdd� Zdd� Zdd� Zdd� Zdd � Zd!d"� Zd#d$� Zd%d&� ZdDd(d)�ZdDd*d+�Zd,d-� Zd.d/� Zd0d1� Zd2d3� Zd4d5� Zd6d7� Zd8d9� Zd:d;� Z d<d=� Z!d>d?� Z"d@dA� Z#dBdC� Z$d'S )E�BlueOceanAnalyzerV2u5   蓝海产品分析器 v2.4 - 智能文件命名版本c                 C   sp   t � | _t| j�| _t| j�| _t� | _t| j| j�| _	g | _
g | _g | _g | _
d| _d | _| ��  | ��  d S )N�image)r   �config_managerr   �
ocr_processorr   �score_calculatorr	   �
data_exporterr
   �
data_importer�results�failed_files�image_files�
data_files�current_mode�last_selected_folder�
setup_logging�	setup_gui��self� r   �RF:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\1111\blue_ocean_analyzer_v2.py�__init__   s   zBlueOceanAnalyzerV2.__init__c                 C   s^   | j �� }tjtt|�dd��dtj|�dd�dd�t�� gd� t�t	�| _
| j
�d	� d
S )u   设置日志记录�levelZINFOz)%(asctime)s - %(levelname)s - %(message)sZlog_filezanalyzer.logzutf-8)�encoding)r   �format�handlersu!   蓝海产品分析器 v2.0 启动N)r
   Zget_logging_config�loggingZbasicConfig�getattr�getZFileHandlerZ
StreamHandlerZ	getLogger�__name__�logger�info)r   Zlogging_configr   r   r   r   /   s   
��z!BlueOceanAnalyzerV2.setup_loggingc                 C   s�   | j �� }t�� | _| j�d� | j�|�dd�� d|�dd�� �� | j�dd� t	�
| j�}|jtjdddd	� | �
|� | �|� | ��  d
S )u   设置GUI界面u   蓝海产品分析器 v2.0Zwindow_widthi�  �xZ
window_heightiR  T�
   )�fill�expand�padx�padyN)r
   Z
get_ui_config�tkZTk�root�titleZgeometryr%   Zminsizer   �Frame�pack�BOTH�setup_control_panel�setup_result_panel�setup_status_bar)r   Z	ui_config�
main_framer   r   r   r   =   s   

&

zBlueOceanAnalyzerV2.setup_guic           	      C   s�  t j|ddd�}|jtjtjdd� t j|ddd�}|jtjdd� t j|d	| jd
�jtjdd� t j|d| j	d
�jtjdd� t j
|d
d�jtjdd� t j|d| jd
�jtjdd� t j|d| jd
�jtjdd� t j|d| j
d
�jtjdd� t j|dd�| _| jjdd� | �|� t j|ddd�}|jtjdd� t �|�}|jtjdd� t j|d| jd
�| _| jjtjtjddd� t j|d| jd
�| _| jjtjtjddd� t j|dd�| _| jjtjdd� t j|ddd�}|jtjd� t �|�}|jtjdd� t j|d | jd
�jtjtjddd� t j|d!| jd
�jtjtjddd� t �|�}|jtjdd� t j|d"| jd
�jtjtjddd� t j|d#| jd
�jtjtjddd� d$S )%u   设置控制面板u   控制面板r*   ��text�padding�r   r*   )�sider+   r-   u   文件选择�   �r+   r.   �   选择图片文件夹�r:   �command�   u   选择单个图片Z
horizontal��orientu   📊 选择Excel文件u   📄 选择JSON文件u   📚 选择多种数据文件�   未选择文件�r:   )r.   u   分析控制�   开始分析T�r   r>   �r=   r+   r,   r-   u   🧹 清空重置�r>   r   Zdeterminate)�modeu   导出结果)r+   u   导出Excelu   筛选后导出Excelu
   导出JSONu   筛选后导出JSONN)r   �
LabelFramer3   r/   �LEFT�Y�X�Button�
select_folder�select_filesZ	Separator�select_excel_files�select_json_files�select_data_files�Label�file_count_label�setup_filter_configr2   �start_analysis�analyze_btn�clear_all_dataZ	clear_btnZProgressbar�progress�export_excel�export_filtered_excel�export_json�export_filtered_json)	r   �parentZ
control_frameZ
file_frameZanalysis_frame�button_frameZexport_frameZexcel_frameZ
json_framer   r   r   r5   S   s�   ����������

��
����
���
�z'BlueOceanAnalyzerV2.setup_control_panelc                 C   s�  t j|ddd�}|jtjdd� t �|�}|jtjdd� t j|ddd�}|jtjtjdd	d
� t j|ddd�}|jtjtjddd
� i | _	| j
�� }g d
�}g d�}t|�D ]7\}	\}
}|
|v r�t j
||d�j|	ddddd� tj||
 d�}|| j	|
< t j||dd�}
|
j|	dddd� qWt|�D ]7\}	\}
}|
|v r�t j
||d�j|	ddddd� tj||
 d�}|| j	|
< t j||dd�}
|
j|	dddd� q�t �|�}|jtjdd� t j|d| jd���  dS )u   设置筛选条件配置u   筛选条件设置r*   r9   r<   r?   T�r+   r,   u   📊 评分标准设置rI   rJ   u   🚫 一票否决权设置rK   ))u   需求供给比_优秀u   需求供给比(优秀))u   需求供给比_良好u   需求供给比(良好))u   转化率_优秀u   转化率(优秀))u   转化率_良好u   转化率(良好))u   天猫占比_低u   天猫占比(优秀))u   天猫占比_中u   天猫占比(良好)))u   否决_搜索人气最小值u   搜索人气最小值)u   否决_支付买家数最小值u   支付买家数最小值)u   否决_需求供给比最小值u   需求供给比最小值)u   否决_天猫占比最大值u   天猫占比最大值(%)rG   r   �wrC   �   )�row�columnZstickyr-   r.   ��value)�textvariable�width�   )r*   rC   )rg   rh   r-   r.   �r*   r   u   🔄 重置默认值rA   N)r   rM   r3   r/   rP   r2   r4   rN   �RIGHT�filter_varsr
   �get_filter_config�	enumeraterW   ZgridZ	DoubleVarZEntryrQ   �
reset_filters)r   rb   Zfilter_framer8   Z
left_frameZright_frame�
filter_configZ
left_itemsZright_itemsrg   �keyZdisplay_name�var�entryrc   r   r   r   rY   �   sL   



�
�
�
�
�
z'BlueOceanAnalyzerV2.setup_filter_configc           
      C   sH  t j|ddd�}|jtjtjdd� t j|tjd�}|jtjdd� t �|�}|j	|dd	� t j
|d
dd���  d
}t j||ddd�| _|D ]}| jj
||d� | jj|ddd� qFt j|d| jjd�}| jj|jd� | jjdddd� |jddd� t �|�}|j	|dd	� t j
|ddd���  d}	t j||	ddd�| _|	D ]}| jj
||d� | jj|ddd� q�t j|d| jjd�}
| jj|
jd� | jjdddd� |
jddd� t j|dd d�}|jtjd!d"� tj|d#tjd$�| _t j|d| jjd�}| jj|jd� | jjdddd� |jddd� | j�d%| j� | j�d%| j� d&S )'u   设置结果显示面板u   分析结果r*   r9   T)r=   r+   r,   rD   rd   rm   )Zweightu   识别数据)Z
TkDefaultFontr*   Zbold)r:   Zfont)�   产品名称�   搜索人气�   支付转化率�   支付买家数�   需求供给比�   天猫占比Zheadings�   )�columnsZshow�heightrG   �P   �center)rl   ZanchorZvertical)rE   rB   )Zyscrollcommand�leftZboth�right�y)r=   r+   u   蓝海分析 (按指数排序))rx   �   蓝海指数r|   u	   转化率r}   �   评级u   详细信息r>   rn   r?   �	   )r�   Zwrapz<<TreeviewSelect>>N)r   rM   r3   r/   ro   r4   ZPanedWindowZ
HORIZONTALr2   �addrW   ZTreeview�raw_treeZheadingrh   Z	ScrollbarZyviewZ	configure�set�treerP   ZTextZWORD�detail_textZbind�on_item_select�on_raw_item_select)
r   rb   Zresult_frameZpaned_windowZ
left_panelZraw_columnsZcolZ
raw_scrollbarZright_panelZresult_columnsZ	scrollbarZdetail_frameZdetail_scrollbarr   r   r   r6   �   sJ   

z&BlueOceanAnalyzerV2.setup_result_panelc                 C   s>   t jdd�| _tj| j| jt jdd�}|jt jt j	dd� dS )u   设置状态栏u   就绪ri   )r>   rf   )rk   Zreliefr;   rI   )r=   r+   r.   N)
r/   Z	StringVar�
status_varr   rW   r0   ZSUNKENr3   ZBOTTOMrP   )r   Z
status_barr   r   r   r7   %  s   z$BlueOceanAnalyzerV2.setup_status_bar�messagec                 C   s   | j �|� | j��  dS )u   更新状态栏信息N)r�   r�   r0   �update_idletasks)r   r�   r   r   r   �
update_status+  s   z!BlueOceanAnalyzerV2.update_statusc                 C   s   | � �  dS )u   显示分析结果N)�update_results_displayr   r   r   r   �display_results0  s   z#BlueOceanAnalyzerV2.display_resultsc                 C   s(   t jdd�}|r|| _| �|� dS dS )r@   u   选择包含图片的文件夹)r1   N)r   Zaskdirectoryr   �_scan_folder_images�r   �folderr   r   r   rR   4  s
   �z!BlueOceanAnalyzerV2.select_folderc                    sd   d�� �fdd�t �� �D �| _d| _g | _| jjdt| j�� d�d� | �dt| j�� d�� d	S )
u!   扫描文件夹中的图片文件)z.pngz.jpgz.jpegz.bmpz.tiffz.gifc                    s(   g | ]}|� � ���rtj�� |��qS r   )�lower�endswith�os�path�join��.0�f�r�   Zimage_extensionsr   r   �
<listcomp>?  s    ��z;BlueOceanAnalyzerV2._scan_folder_images.<locals>.<listcomp>r   �
   已选择 �    个图片文件rG   �?    个图片文件，点击'开始分析'进行OCR识别和分析N)	r�   �listdirr   r   r   rX   �config�lenr�   r�   r   r�   r   r�   ;  s   �z'BlueOceanAnalyzerV2._scan_folder_imagesc                 C   sh   t jdddgd�}|r2t|�| _d| _g | _| jjdt| j�� d�d� | �	dt| j�� d	�� d
S d
S )u!   选择单个或多个图片文件u   选择图片文件)u   图片文件z%*.png *.jpg *.jpeg *.bmp *.tiff *.gif�u   所有文件z*.*�r1   �	filetypesr   r�   r�   rG   r�   N)
r   �askopenfilenames�listr   r   r   rX   r�   r�   r�   )r   �filesr   r   r   rS   H  s   ��
�z BlueOceanAnalyzerV2.select_filesc                 C   s8   | j �� }| j�� D ]\}}||v r|�|| � q
dS )u   重置筛选条件为默认值N)r
   rq   rp   �itemsr�   )r   rt   ru   rv   r   r   r   rs   X  s   
��z!BlueOceanAnalyzerV2.reset_filtersc              
   C   sf  | j dko	| jdu}| j}g | _g | _g | _d| _ d| jd< | jjddd� | j�	� D ]}| j�
|� q+| j�	� D ]}| j�
|� q9| j�
dt
j� | j�dd	� |r�|r�tj�|�r�z| �|� | �d
t| j�� d�� | j�d|� �� W n3 ty� } z| jjd
d� | �d� | j�d|� �� W Y d}~nd}~ww | jjd
d� | �d� | j�d� dS )u!   清空所有数据和重置界面r   Nr   rj   �normalrH   ��stater:   �      �?u3   请选择图片文件或数据文件开始分析...u%   已刷新文件夹，重新扫描到 r�   u&   清空重置后自动刷新文件夹: rF   rG   u?   界面已重置，文件夹刷新失败，请重新选择文件u   文件夹刷新失败: u3   界面已重置，请重新选择文件进行分析u!   用户执行了清空重置操作)r   r   r   r   r   r]   r[   r�   r�   �get_children�deleter�   r�   r/   �END�insertr�   r�   �existsr�   r�   r�   r'   r(   �	ExceptionrX   �warning)r   Zwas_folder_modeZlast_folder�item�er   r   r   r\   _  s8   


��
z"BlueOceanAnalyzerV2.clear_all_datac                 C   s�   | j dkr| jst�dd� dS | j dkr | js t�dd� dS i }| j�� D ]
\}}|�� ||< q'| j�	|� g | _
g | _| jj
ddd	� | j dkr]| jj
t| j�d
� tj| jd�}n| jj
t| j�d
� tj| jd�}d|_|��  dS )
rH   r   �   警告u'   请先选择要分析的图片文件！N�datau'   请先选择要分析的数据文件！Zdisabledu   分析中...r�   )Zmaximum)�targetT)r   r   r   �showwarningr   rp   r�   r%   r
   Zupdate_filter_configr   r   r[   r�   r]   r�   �	threadingZThread�analyze_images�analyze_data_filesZdaemon�start)r   Z
new_configru   rv   Zanalysis_threadr   r   r   rZ   �  s(   
z"BlueOceanAnalyzerV2.start_analysisc                 C   s*  d}t | j�D ]�\}}zV| j�dtj�|�� d|d � dt| j�� d�� | j�	|�}|rI|D ]}|rCd|v rC| j
�|�}| j�
|� q/|d7 }n	| j�
|dd	�� | jj|d d
� | j��  W q ty� } z| j�
|t|�d	�� | j�d|� d|� �� W Y d
}~qd
}~ww | j�d| j� d
S )u   分析图片r   u   正在分析: � (rm   �/�)rx   u   未识别到有效产品数据)�file�reasonri   u   分析图片失败 z: N)rr   r   r�   r�   r�   r�   �basenamer�   r   Z
process_imager   �calculate_blue_ocean_scorer   �appendr   r]   r�   r0   r�   r�   �strr'   �errorZafter�analysis_complete)r   Zsuccessful_count�iZ
image_pathZ
products_dataZproduct_dataZanalysisr�   r   r   r   r�   �  s6   2�
��$��z"BlueOceanAnalyzerV2.analyze_imagesc                 C   sb   | j jddd� | j�dt| j�� d�� | ��  | j�| j�}| j	�
dtj� | j	�
d|� dS )u   分析完成后的UI更新r�   rH   r�   u   分析完成！成功: u	   个产品r�   N)r[   r�   r�   r�   r�   r   r�   r   Zgenerate_summary_reportr�   r�   r/   r�   r�   )r   Zsummaryr   r   r   r�   �  s   z%BlueOceanAnalyzerV2.analysis_completec              
   C   s�   | j �� D ]}| j �|� q| j�� D ]}| j�|� q| js!dS | jD ]'}| jjdd|d |�dd�|�dd�|�dd�|�d	d�|�d
d�fd� q$t| jdd
� dd�}|D ]#}| j jdd|d |d |�d	d�|�dd�|�d
d�|d fd� qXdS )u   更新结果显示N� �endrx   ry   u	   未识别rz   r{   r|   r}   )�valuesc                 S   s   | d S )Nr�   r   )r)   r   r   r   �<lambda>�  s    z<BlueOceanAnalyzerV2.update_results_display.<locals>.<lambda>T)ru   �reverser�   r�   )r�   r�   r�   r�   r   r�   r%   �sorted)r   r�   �resultZsorted_resultsr   r   r   r�   �  s4   







�





��z*BlueOceanAnalyzerV2.update_results_displayNc                    �j   | j �� }|r1| j �|d �}|d r|d d nd� t� fdd�| jD �d�}|r3| �|� dS dS dS )u!   选择项目时显示详细信息r   r�   r�   c                 3   �"   � | ]}|� d �� kr|V  qdS �rx   N�r%   �r�   �r�Zproduct_namer   r   �	<genexpr>  �   �  z5BlueOceanAnalyzerV2.on_item_select.<locals>.<genexpr>N)r�   �	selectionr�   �nextr   �show_product_details�r   �eventr�   r�   r�   r   r�   r   r�     �   
�z"BlueOceanAnalyzerV2.on_item_selectc                    r�   )u-   选择原始数据项目时显示详细信息r   r�   r�   c                 3   r�   r�   r�   r�   r�   r   r   r�     r�   z9BlueOceanAnalyzerV2.on_raw_item_select.<locals>.<genexpr>N)r�   r�   r�   r�   r   r�   r�   r   r�   r   r�     r�   z&BlueOceanAnalyzerV2.on_raw_item_selectc                 C   sh  |� dd�}|� dd�}|� dd�}|� dd�}|� dd�}|� dd�}|� d	d�}|� d
d�}	|� dd�}
|� dd�}d
|� dd�� d|� dd�� d|� dd�� d|� dd�� d|d�d|d�d|d�d|d�d|d�d|d�d|d�d|	d�d|
d�d|d�d�}d|v r�|d D ]
}
|d|
� d�7 }q�d|v r�|d |d � �7 }| j�d!tj� | j�d!|� d"S )#u   显示产品详细信息ry   �-rz   r{   r|   r   r}   u   搜索人气增长u   转化率增长u   买家数增长u   需求供给比增长u   天猫占比增长u   产品名称: rx   u   未知u   
文件名: u	   文件名u   
蓝海指数: r�   u
   /100
评级: r�   uo   

基础数据:
    搜索人气      支付转化率    支付买家数     需求供给比      天猫占比
z>12z  z>10�
u   

分析详情:
u
    分析详情�   分析详情u   • �   否决原因u   
🚫 否决原因: r�   N)r%   r�   r�   r/   r�   r�   )r   r�   Z
search_volumeZconversion_rateZbuyersZ
supply_demandZtmall_ratioZ
search_growthZconversion_growthZ
buyers_growthZsupply_demand_growthZtmall_growthZdetailsZdetailr   r   r   r�     sX   
�
�
����������
�
z(BlueOceanAnalyzerV2.show_product_detailsc                 C   �R   | j st�dd� dS | �dd�}tjdddg|d�}|r'| j�| j |� dS dS )	u   导出Excel文件r�   �   没有分析结果可导出！N�	   数据源�.xlsx��   Excel文件z*.xlsxr�   ��defaultextensionr�   �initialfile)r   r   r�   �_generate_filenamer   �asksaveasfilenamer   �export_to_excel�r   �default_filename�filenamer   r   r   r^   C  �   ��z BlueOceanAnalyzerV2.export_excelc                 C   r�   )	u   导出JSON文件r�   r�   Nr�   �.json�u
   JSON文件z*.jsonr�   r�   )r   r   r�   r�   r   r�   r   �export_to_jsonr�   r   r   r   r`   S  r�   zBlueOceanAnalyzerV2.export_jsonc                 C   s   dd� | j D �S )u?   获取筛选后的结果（排除一票否决的红海产品）c                 S   s   g | ]}d |vr|�qS )r�   r   )r�   r�   r   r   r   r�   e  s
    �z=BlueOceanAnalyzerV2._get_filtered_results.<locals>.<listcomp>)r   r   r   r   r   �_get_filtered_resultsc  s   �z)BlueOceanAnalyzerV2._get_filtered_resultsc                 C   s"   t �� �d�}d|� d|� |� �S )u  生成带时间戳的文件名

        Args:
            source_type: "数据源" 或 "筛选源"
            file_extension: ".xlsx" 或 ".json"

        Returns:
            生成的文件名，格式：蓝海分析_数据源_2025-07-23_18-30-45.xlsx
        z%Y-%m-%d_%H-%M-%Su
   蓝海分析_�_)r   Znow�strftime)r   Zsource_typeZfile_extensionZ	timestampr   r   r   r�   j  s   
z&BlueOceanAnalyzerV2._generate_filenamec                 C   �   | j st�dd� dS | �� }|st�dd� dS | �dd�}tjdddgd	|d
�}|rA| j�||� t�	ddt
|�� d
�� dS dS )uD   导出筛选后的Excel文件（排除一票否决的红海产品）r�   r�   N�!   筛选后没有数据可导出！�	   筛选源r�   r�   r�   u   保存筛选后的Excel文件�r�   r�   r1   r�   �   导出成功�
   已导出 uC    个产品到Excel文件
（已排除一票否决的红海产品）)r   r   r�   r�   r�   r   r�   r   r�   �showinfor�   �r   Zfiltered_resultsr�   r�   r   r   r   r_   w  �$   ��z)BlueOceanAnalyzerV2.export_filtered_excelc                 C   r  )uC   导出筛选后的JSON文件（排除一票否决的红海产品）r�   r�   Nr  r  r�   r�   r�   u   保存筛选后的JSON文件r  r  r  uB    个产品到JSON文件
（已排除一票否决的红海产品）)r   r   r�   r�   r�   r   r�   r   r�   r  r�   r  r   r   r   ra   �  r	  z(BlueOceanAnalyzerV2.export_filtered_jsonc                 C   �h   t jd ddgd�}|r2t|�| _d| _g | _| jjdt| j�� d�d� | �	dt| j�� d�� d	S d	S )
u   选择Excel文件�r�   z*.xlsx *.xlsr�   r�   r�   r�   u    个Excel文件rG   u2    个Excel文件，点击'开始分析'进行处理N�
r   r�   r�   r   r   r   rX   r�   r�   r�   �r   �	filenamesr   r   r   rT   �  �   �
�z&BlueOceanAnalyzerV2.select_excel_filesc                 C   r
  )
u   选择JSON文件r�   r�   r�   r�   r�   u    个JSON文件rG   u1    个JSON文件，点击'开始分析'进行处理Nr  r
  r   r   r   rU   �  r  z%BlueOceanAnalyzerV2.select_json_filesc                 C   s�   t jdg d�d�}|rLt|�| _d| _g | _tdd� | jD ��}tdd� | jD ��}| jjdt	| j�� d	|� d
|� d�d� | �
dt	| j�� d
�� dS dS )u   选择多种数据文件u*   选择数据文件（支持Excel和JSON）))u   支持的文件z*.xlsx *.xls *.jsonr  r�   r�   r�   r�   c                 s   �"   � | ]}|� � �d �rdV  qdS ))r�   z.xlsrm   N�r�   r�   r�   r   r   r   r�   �  r�   z8BlueOceanAnalyzerV2.select_data_files.<locals>.<genexpr>c                 s   r  )r�   rm   Nr  r�   r   r   r   r�   �  r�   r�   u    个数据文件 (Excel:z, JSON:r�   rG   u3    个数据文件，点击'开始分析'进行处理N)r   r�   r�   r   r   r   �sumrX   r�   r�   r�   )r   r  Zexcel_countZ
json_countr   r   r   rV   �  s   �

(�z%BlueOceanAnalyzerV2.select_data_filesc           
      C   s�  �zL�z| j �dt| j�� d�� | �d� | j�| j�}|s5| �d� W W | jjddd� d| j	d	< d
S | �d� | j�
|�}| �dt|�� d
�� t|�D ]\}}z4| j�
|�}| j�|� t|d t|� d �}|d | j	d	< | �d|d � dt|�� d|� d�� W qO ty� } z;| j �d|d � d|� �� |�� }d|d< d|d< dt|�� �g|d< | j�|� | j�d|d � �� W Y d
}~qOd
}~ww | �d� | j�| j�| _| ��  t| j�t| j� }	| �d|	� dt| j�� d�� | j �d t| j�� d!�� W n& t�y- } z| j �d"|� �� | �d#t|�� �� W Y d
}~nd
}~ww W | jjddd� d| j	d	< d
S W | jjddd� d| j	d	< d
S | jjddd� d| j	d	< w )$u   分析数据文件u
   开始分析 u    个数据文件u   正在导入数据文件...u   数据导入失败r�   rH   r�   r   rj   Nu   正在合并和去重数据...u   正在重新分析 u
    条数据...rm   �d   u   正在分析数据 r�   r�   z%)u
   分析第 u    条数据失败: r�   u   ❌ 分析失败r�   u   重新分析失败: r�   u
   数据项_u   正在按蓝海指数排序...u   数据分析完成: 成功 u    条，失败 u    条u!   数据文件分析完成: 总计 u
    条数据u   分析数据文件失败: u   分析失败: )r'   r(   r�   r   r�   r   Zbatch_import_filesr[   r�   r]   Zmerge_and_deduplicate_datarr   r   r�   r   r�   �intr�   r�   �copyr�   r   Zsort_by_blue_ocean_scorer�   r�   )
r   Zall_imported_dataZmerged_datar�   r�   r�   r]   r�   Zfailed_itemZ
success_countr   r   r   r�   �  s`   

0
�*"��
 �����z&BlueOceanAnalyzerV2.analyze_data_filesc                 C   s   | j ��  dS )u   运行程序N)r0   �mainloopr   r   r   r   �run  s   zBlueOceanAnalyzerV2.run)N)%r&   �
__module__�__qualname__�__doc__r   r   r   r5   rY   r6   r7   r�   r�   r�   rR   r�   rS   rs   r\   rZ   r�   r�   r�   r�   r�   r�   r^   r`   r�   r�   r_   ra   rT   rU   rV   r�   r  r   r   r   r   r      sD    HFD
. &
"


'
?r   �__main__)r  r�   Ztkinterr/   r   r   r   r�   r#   r   r
   r   r   r   r   r   r   r	   r   r
   r   r&   �appr  r   r   r   r   �<module>   s.         �