# 蓝海产品分析器 v3.0 - 依赖包列表
# 使用方法: pip install -r requirements_lanhai.txt

# 核心OCR依赖 - PaddleOCR 3.1版本
paddleocr==3.1.0
paddlepaddle>=2.5.0

# 图像处理依赖
pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0

# 数据处理依赖
pandas>=2.0.0
openpyxl>=3.1.0

# GUI界面依赖 (Python内置，无需安装)
# tkinter - Python内置库

# 可选：GPU加速支持 (如果有NVIDIA GPU)
# paddlepaddle-gpu>=2.5.0  # 取消注释以启用GPU支持

# 开发和调试工具 (可选)
# pytest>=7.0.0
# black>=22.0.0
