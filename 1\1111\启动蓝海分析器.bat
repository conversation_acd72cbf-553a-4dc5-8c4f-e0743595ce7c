@echo off
chcp 65001 >nul
title 蓝海产品分析器 v2.0

echo.
echo ========================================
echo    🎯 蓝海产品分析器 v2.0 启动中...
echo ========================================
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Python环境
    echo.
    echo 请先安装Python 3.7或更高版本：
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检测成功
echo.

:: 检查必要文件是否存在
if not exist "启动分析器.py" (
    echo ❌ 错误：找不到启动分析器.py文件
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo ✅ 程序文件检测成功
echo.

:: 检查并安装依赖
echo 🔧 检查Python依赖包...
python -c "import pandas, paddleocr, openpyxl, PIL" >nul 2>&1
if errorlevel 1 (
    echo.
    echo 📦 正在安装必要的依赖包...
    echo 这可能需要几分钟时间，请耐心等待...
    echo.
    
    pip install pandas paddleocr openpyxl Pillow
    
    if errorlevel 1 (
        echo.
        echo ❌ 依赖安装失败，尝试使用国内镜像源...
        pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ pandas paddleocr openpyxl Pillow
        
        if errorlevel 1 (
            echo.
            echo ❌ 依赖安装失败，请检查网络连接或手动安装
            echo.
            echo 手动安装命令：
            echo pip install pandas paddleocr openpyxl Pillow
            echo.
            pause
            exit /b 1
        )
    )
    
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖包检查通过
)

echo.
echo 🚀 启动蓝海产品分析器...
echo.

:: 启动程序
python "启动分析器.py"

:: 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 程序运行出现错误
    echo 请检查错误信息或联系技术支持
    echo.
    pause
)

echo.
echo 程序已退出，感谢使用！
pause
