@echo off
:: 设置控制台编码为UTF-8
chcp 65001 >nul 2>&1
:: 设置环境变量确保正确显示中文
set PYTHONIOENCODING=utf-8
title 蓝海产品分析器 v3.0 - PaddleOCR 3.1版本

echo.
echo ========================================
echo   蓝海产品分析器 v3.0 启动器
echo   PaddleOCR 3.1版本 - 数字识别完美解决
echo ========================================
echo.

:: 切换到脚本所在目录 (支持跨盘符)
cd /d "%~dp0"

:: 检查并激活lanhai虚拟环境
echo 检查lanhai虚拟环境...

:: 尝试多个可能的环境路径
set "LANHAI_FOUND=0"
set "PYTHON_EXE="

:: 路径1: C:\Users\<USER>\lanhai (用户指定的路径)
if exist "C:\Users\<USER>\lanhai\Scripts\python.exe" (
    echo 找到lanhai环境: C:\Users\<USER>\lanhai
    set "PYTHON_EXE=C:\Users\<USER>\lanhai\Scripts\python.exe"
    set "PIP_EXE=C:\Users\<USER>\lanhai\Scripts\pip.exe"
    if exist "C:\Users\<USER>\lanhai\Scripts\activate.bat" (
        call "C:\Users\<USER>\lanhai\Scripts\activate.bat"
    )
    set "LANHAI_FOUND=1"
    goto :env_activated
)

:: 路径2: 当前用户目录下的lanhai
if exist "%USERPROFILE%\lanhai\Scripts\python.exe" (
    echo 找到lanhai环境: %USERPROFILE%\lanhai
    set "PYTHON_EXE=%USERPROFILE%\lanhai\Scripts\python.exe"
    set "PIP_EXE=%USERPROFILE%\lanhai\Scripts\pip.exe"
    if exist "%USERPROFILE%\lanhai\Scripts\activate.bat" (
        call "%USERPROFILE%\lanhai\Scripts\activate.bat"
    )
    set "LANHAI_FOUND=1"
    goto :env_activated
)

:: 路径3: 项目目录下的lanhai
if exist "%~dp0lanhai\Scripts\python.exe" (
    echo 找到lanhai环境: %~dp0lanhai
    set "PYTHON_EXE=%~dp0lanhai\Scripts\python.exe"
    set "PIP_EXE=%~dp0lanhai\Scripts\pip.exe"
    if exist "%~dp0lanhai\Scripts\activate.bat" (
        call "%~dp0lanhai\Scripts\activate.bat"
    )
    set "LANHAI_FOUND=1"
    goto :env_activated
)

:: 路径4: conda环境
where conda >nul 2>&1
if %errorlevel% == 0 (
    echo 尝试激活conda lanhai环境...
    call conda activate lanhai >nul 2>&1
    if %errorlevel% == 0 (
        echo conda lanhai环境已激活
        set "PYTHON_EXE=python"
        set "PIP_EXE=pip"
        set "LANHAI_FOUND=1"
        goto :env_activated
    )
)

:env_not_found
echo 未找到lanhai环境，使用系统Python
echo 建议创建lanhai环境以获得最佳体验:
echo    conda create -n lanhai python=3.10
echo    conda activate lanhai
echo    pip install paddleocr==3.1.0 pandas openpyxl pillow
set "PYTHON_EXE=python"
set "PIP_EXE=pip"
goto :continue

:env_activated
echo lanhai环境已激活
echo PaddleOCR 3.1版本已就绪

:continue

echo.
echo 启动蓝海产品分析器...
echo.

:: 检查Python和依赖
echo 检查Python环境...
"%PYTHON_EXE%" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python未找到！请确保Python已正确安装
    echo 请访问 https://python.org 下载安装Python
    goto :error_exit
)

echo 检查PaddleOCR版本...
"%PYTHON_EXE%" -c "import paddleocr; print('PaddleOCR版本:', paddleocr.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo PaddleOCR未安装或版本不正确
    echo 正在尝试安装PaddleOCR 3.1.0...
    "%PIP_EXE%" install paddleocr==3.1.0 --quiet
    if %errorlevel% neq 0 (
        echo PaddleOCR安装失败
        echo 请手动运行: pip install paddleocr==3.1.0
        goto :error_exit
    )
    echo PaddleOCR 3.1.0 安装成功
)

echo.
echo 启动蓝海产品分析器 v3.0...
echo 数字识别问题已完美解决！
echo.

:: 运行主程序
"%PYTHON_EXE%" main.py

:: 检查程序退出状态
if %errorlevel% == 0 (
    echo.
    echo ========================================
    echo   程序正常退出
    echo   感谢使用蓝海产品分析器 v3.0
    echo ========================================
) else (
    echo.
    echo ========================================
    echo   程序异常退出 (错误代码: %errorlevel%)
    echo   请检查错误信息或联系技术支持
    echo ========================================
)

echo.
echo 提示: 虚拟环境将在窗口关闭时自动退出
echo 按任意键退出...
pause >nul
goto :end

:error_exit
echo.
echo ========================================
echo   启动失败
echo   请检查环境配置后重试
echo ========================================
echo.
echo 按任意键退出...
pause >nul

:end
