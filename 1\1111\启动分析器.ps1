# 蓝海产品分析器 PowerShell 启动脚本
# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🎯 蓝海产品分析器 v2.0 启动中..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 切换到脚本所在目录
Set-Location $PSScriptRoot

# 检查Python是否安装
try {
    $pythonVersion = python --version 2>$null
    Write-Host "✅ Python环境检测成功: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误：未检测到Python环境" -ForegroundColor Red
    Write-Host ""
    Write-Host "请先安装Python 3.7或更高版本：" -ForegroundColor Yellow
    Write-Host "https://www.python.org/downloads/" -ForegroundColor Blue
    Write-Host ""
    Read-Host "按回车键退出"
    exit 1
}

# 检查程序文件
if (-not (Test-Path "启动分析器.py")) {
    Write-Host "❌ 错误：找不到启动分析器.py文件" -ForegroundColor Red
    Write-Host "请确保在正确的目录中运行此脚本" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "✅ 程序文件检测成功" -ForegroundColor Green
Write-Host ""

# 检查依赖
Write-Host "🔧 检查Python依赖包..." -ForegroundColor Yellow
try {
    python -c "import pandas, paddleocr, openpyxl, PIL" 2>$null
    Write-Host "✅ 依赖包检查通过" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "📦 正在安装必要的依赖包..." -ForegroundColor Yellow
    Write-Host "这可能需要几分钟时间，请耐心等待..." -ForegroundColor Cyan
    Write-Host ""
    
    try {
        pip install pandas paddleocr openpyxl Pillow
        Write-Host "✅ 依赖安装完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 依赖安装失败，尝试使用国内镜像源..." -ForegroundColor Red
        try {
            pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ pandas paddleocr openpyxl Pillow
            Write-Host "✅ 依赖安装完成" -ForegroundColor Green
        } catch {
            Write-Host "❌ 依赖安装失败，请检查网络连接" -ForegroundColor Red
            Write-Host ""
            Write-Host "手动安装命令：" -ForegroundColor Yellow
            Write-Host "pip install pandas paddleocr openpyxl Pillow" -ForegroundColor White
            Write-Host ""
            Read-Host "按回车键退出"
            exit 1
        }
    }
}

Write-Host ""
Write-Host "🚀 启动蓝海产品分析器..." -ForegroundColor Green
Write-Host ""

# 启动程序
try {
    python "启动分析器.py"
} catch {
    Write-Host ""
    Write-Host "❌ 程序运行出现错误" -ForegroundColor Red
    Write-Host "请检查错误信息或联系技术支持" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按回车键退出"
}

Write-Host ""
Write-Host "程序已退出，感谢使用！" -ForegroundColor Cyan
Read-Host "按回车键退出"
