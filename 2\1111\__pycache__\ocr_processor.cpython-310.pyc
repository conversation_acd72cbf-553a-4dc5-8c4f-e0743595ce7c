o

    λ�h93  �                   @   sF   d Z ddlZddlZddlZddlmZmZmZmZ G dd� d�Z	dS )u0   
OCR处理模块
负责OCR识别和数据提取
�    N)�List�Dict�Any�Optionalc                	   @   s�   e Zd ZdZdd� Zdd� Zdedefdd	�Zd
edefdd�Z	d
edefdd�Z
dee deee  fdd�Z
d
edefdd�Zdededeeeef  fdd�Zdedeeeef  fdd�ZdS )�OCRProcessoru   OCR处理器c                 C   s   || _ t�t�| _d | _d S )N)�config_manager�logging�	getLogger�__name__�logger�ocr)�selfr   � r   �IF:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\1111\ocr_processor.py�__init__   s   
zOCRProcessor.__init__c              
   C   s�   | j du rDz(ddlm} | j�� }||�dd�|�dd�|�dd	�d
�| _ | j�d� W dS  tyC } z
| j�	d|� �� � d}~ww dS )
u   延迟初始化OCRNr   )�	PaddleOCR�
use_angle_clsT�lang�ch�use_gpuF)r   r   r   u   OCR初始化成功u   OCR初始化失败: )
r   Z	paddleocrr   r   �get_ocr_config�getr   �info�	Exception�error)r
   r   �
ocr_config�er   r   r   �init_ocr   s    




����zOCRProcessor.init_ocr�value�returnc              	   C   sF   |r|dkrdS t �ddt|��}zt|�W S  ttfy"   Y dS w )u   安全的浮点数转换�-g        z[^\d.]� )�re�sub�str�float�
ValueError�	TypeError)r
   r   Zclean_valuer   r   r   �safe_float_convert%   s   
�zOCRProcessor.safe_float_convert�	range_strc                 C   s6   |s|S t |��dd��dd�}t�dd|��� }|S )u   标准化范围格式�~z ~ r    z\s+� )r$   �replacer"   r#   �strip)r
   r)   Zstandardizedr   r   r   �standardize_range_format2   s
   z%OCRProcessor.standardize_range_format�textc                 C   s4   ddddd�}|}|� � D ]
\}}|�||�}q
|S )u   修复常见的OCR识别错误z	5% ~ 7.5%z-5%u   万 ~ u   2万 ~ 4万)z% L ~ %z%S-u   万~u	   2万~4万)�itemsr,   )r
   r/   Z	ocr_fixesZ
fixed_textr   Z
correctionr   r   r   �fix_ocr_errors>   s   �zOCRProcessor.fix_ocr_errors�
text_itemsc                 C   s�  t |dd� d�}g }g }d}d}|D ]y}|du s"t|d | �|kr8|�|� |du r/|d n||d  d }q|r�|jdd� d� t|�d	krz|d
d� }|dd
� }	|jdd� d� |	| }dd
� |D �}
tdt|�d � d|
d
d� � d�� |�dd
� |D �� |g}|d }q|r�|jdd� d� t|�d	kr�|d
d� }|dd
� }	|jdd� d� |	| }dd
� |D �}
tdt|�d � d|
d
d� � d�� |�dd
� |D �� |S )u   将文本按行分组c                 S   �   | d S �N�yr   ��xr   r   r   �<lambda>Q   �    z2OCRProcessor.group_texts_by_rows.<locals>.<lambda>)�keyN�   r5   �   c                 S   r3   �Nr7   r   r6   r   r   r   r8   _   r9   �   �����c                 S   r3   r4   r   r6   r   r   r   r8   h   r9   c                 S   �"   g | ]
}|d  |d |d f�qS �r/   r7   r5   r   ��.0�itemr   r   r   �
<listcomp>m   �   " z4OCRProcessor.group_texts_by_rows.<locals>.<listcomp>u      调试 - 第u"   行最后两列坐标(已修正): u    (天猫占比和增长率)c                 S   �   g | ]}|d  �qS �r/   r   rB   r   r   r   rE   p   �    c                 S   r3   r=   r   r6   r   r   r   r8   v   r9   c                 S   r3   r4   r   r6   r   r   r   r8      r9   c                 S   r@   rA   r   rB   r   r   r   rE   �   rF   c                 S   rG   rH   r   rB   r   r   r   rE   �   rI   )�sorted�abs�append�sort�len�print)r
   r2   Zsorted_items�rowsZcurrent_rowZ	current_yZy_thresholdrD   Zlast_twoZ
other_colsZrow_with_coordsr   r   r   �group_texts_by_rowsN   s@   
"&
&z OCRProcessor.group_texts_by_rowsc                 C   sd   |r
t |�� �dk rdS g d�}|�� }|D ]	}||v r dS qt�d|�r(dS t�d|�r0dS dS )u$   判断是否为有效的产品名称�   F)u   相关搜索词u   相关搜素词�   搜索人气u   搜素人气�   支付转化率�   支付买家数�   需求供给比u   天猫商品点击占比�   天猫占比u   天猫商品点击占比○u	   转化率u	   买家数u	   供给比u   点击占比�   产品名称u   产品分析u   ^[\d\s%~\-+.,万]+$u%   ^\d+[万千]?\s*[~\-]\s*\d+[万千]?$T)rN   r-   r"   �match)r
   r/   Zheader_keywordsZ
text_clean�keywordr   r   r   �is_valid_product_name�   s   �z"OCRProcessor.is_valid_product_name�
ocr_result�
image_pathc                    s0  |r|d sg S t dtj�|�� d�� g }|d D ]I}t|�dkrct|d t�rc|d \}}|dkrc|d }|d d |d d  d }|�|�� ||d d |d�� t d|�� � d	|d
�d�� qt dt|�� d
�� � �	|�}	t dt|	�� d�� t
|	d�D ]\}
}t d|
� dt|�� d|� �� q�g }t
|	d�D �]m\}
}t d|
� d�� |s�q�|d �� }
t d|
� d�� � �|
�s�t d|
� d�� q�t d|
� �� t d|� �� |
tj�|�d�}� fdd�|D �}||kr�t d|� �� |}t|�dk�r�zb� �|d �|d < |d |d!< � �|d" �|d#< |d$ |d%< � �|d& �|d'< |d( |d)< � �
|d* �|d+< |d, |d-< |d. |d/< |d0 |d1< t d2|d* � d3|d, � d4|d. � d5|d0 � �� W n� ttf�y� } z� j�d6|� d7|� �� W Y d8}~q�d8}~ww t|�d(k�r�zJ� �|d �|d < � �|d �|d#< � �|d" �|d'< � �
|d$ �|d+< |d& |d/< d9|d!< d9|d%< d9|d)< d9|d-< d9|d1< t d:|d$ � d;�� W n, ttf�y� } z� j�d<|� d7|� �� W Y d8}~q�d8}~ww t d=t|�� d>�� q�t d?|� �� |�|� q�t d@t|�� dA�� |S )Bu!   从OCR结果中提取产品数据r   u   
=== 开始处理图片: z ===r<   �   g333333�?)r/   r5   r7   �
confidenceu   OCR识别: 'u   ' (置信度: z.2f�)u   
OCR识别到 u
    个文本项u   
识别到 u    行数据:u   第u   行 (u   列): u   
--- 处理第u   行 ---u   产品名称候选: '�'u   ❌ 跳过: u    (表头或无效)u   ✅ 有效产品: u      完整行数据: )rX   u	   文件名c                    s   g | ]}� � |��qS r   )r1   )rC   Zcell�r
   r   r   rE   �   s    z5OCRProcessor.extract_product_data.<locals>.<listcomp>u      🔧 OCR修正后: r>   rS   u   搜索人气增长rR   rT   �   u   转化率增长�   rU   �   u   买家数增长�   rV   �   u   需求供给比增长�	   rW   �
   u   天猫占比增长u*      ✅ 11列数据映射: 需求供给比=u   , 需求供给比增长=u   , 天猫占比=u   , 天猫占比增长=u   11列数据转换错误: u
   , 行数据: Nr    u)      ✅ 6列数据映射: 需求供给比=u   , 无趋势数据u   6列数据转换错误: u      ❌ 跳过: 列数不足 (z < 6)u   ✅ 最终产品数据: u   
=== 最终识别到 u    个有效产品 ===)rO   �os�path�basenamerN   �
isinstance�tuplerL   r-   rQ   �	enumerater[   r.   r(   r&   �
IndexErrorr   �warning)r
   r\   r]   r2   rD   r/   r_   �bboxZy_centerrP   �i�rowZproductsZproduct_name_candidate�productZ	fixed_rowr   r   rb   r   �extract_product_data�   s�   
��
 

�4����z!OCRProcessor.extract_product_datac              
   C   sr   z| j du r
| ��  | j � |�}| �||�}|W S  ty8 } z| j�d|� d|� �� g W  Y d}~S d}~ww )u   处理单张图片Nu   处理图片失败 z: )r   r   rv   r   r   r   )r
   r]   �result�
products_datar   r   r   r   �
process_image,  s   
��zOCRProcessor.process_imageN)r
   �
__module__�__qualname__�__doc__r   r   r$   r%   r(   r.   r1   r   r   rQ   �boolr[   r   rv   ry   r   r   r   r   r   
   s    
=" "r   )
r|   rj   r"   r   �typingr   r   r   r   r   r   r   r   r   �<module>   s   