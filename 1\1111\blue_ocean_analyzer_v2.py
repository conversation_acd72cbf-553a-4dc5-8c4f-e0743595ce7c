#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蓝海产品分析器 v2.0 - 模块化重构版本
基于OCR的电商数据分析工具，支持批量图片分析，可调节筛选条件
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import logging
from typing import List, Dict, Any

# 导入自定义模块
from config_manager import ConfigManager
from ocr_processor import OCRProcessor
from score_calculator import ScoreCalculator
from data_exporter import DataExporter
from data_importer import DataImporter

class BlueOceanAnalyzerV2:
    """蓝海产品分析器 v2.0 - 模块化版本"""
    
    def __init__(self):
        # 初始化模块
        self.config_manager = ConfigManager()
        self.ocr_processor = OCRProcessor(self.config_manager)
        self.score_calculator = ScoreCalculator(self.config_manager)
        self.data_exporter = DataExporter()
        self.data_importer = DataImporter(self.config_manager, self.score_calculator)
        
        # 初始化数据
        self.results = []
        self.failed_files = []
        self.image_files = []
        self.data_files = []  # 存储Excel/JSON文件列表
        self.current_mode = 'image'  # 当前模式：'image' 或 'data'
        
        # 设置日志
        self.setup_logging()
        
        # 设置GUI
        self.setup_gui()
    
    def setup_logging(self):
        """设置日志记录"""
        logging_config = self.config_manager.get_logging_config()
        logging.basicConfig(
            level=getattr(logging, logging_config.get('level', 'INFO')),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(logging_config.get('log_file', 'analyzer.log'), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("蓝海产品分析器 v2.0 启动")
    
    def setup_gui(self):
        """设置GUI界面"""
        ui_config = self.config_manager.get_ui_config()
        
        self.root = tk.Tk()
        self.root.title("蓝海产品分析器 v2.0")
        self.root.geometry(f"{ui_config.get('window_width', 1200)}x{ui_config.get('window_height', 850)}")
        self.root.minsize(1200, 850)  # 设置最小窗口大小
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        self.setup_control_panel(main_frame)
        
        # 右侧结果显示
        self.setup_result_panel(main_frame)
        
        # 底部状态栏
        self.setup_status_bar()
    
    def setup_control_panel(self, parent):
        """设置控制面板"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding=10)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 文件选择
        file_frame = ttk.LabelFrame(control_frame, text="文件选择", padding=5)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(file_frame, text="选择图片文件夹",
                  command=self.select_folder).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="选择单个图片",
                  command=self.select_files).pack(fill=tk.X, pady=2)

        # 分隔线
        ttk.Separator(file_frame, orient='horizontal').pack(fill=tk.X, pady=5)

        # 数据文件选择按钮
        ttk.Button(file_frame, text="📊 选择Excel文件",
                  command=self.select_excel_files).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="📄 选择JSON文件",
                  command=self.select_json_files).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="📚 选择多种数据文件",
                  command=self.select_data_files).pack(fill=tk.X, pady=2)

        self.file_count_label = ttk.Label(file_frame, text="未选择文件")
        self.file_count_label.pack(pady=2)
        
        # 筛选条件设置
        self.setup_filter_config(control_frame)
        
        # 分析控制
        analysis_frame = ttk.LabelFrame(control_frame, text="分析控制", padding=5)
        analysis_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.analyze_btn = ttk.Button(analysis_frame, text="开始分析", 
                                     command=self.start_analysis)
        self.analyze_btn.pack(fill=tk.X, pady=2)
        
        self.progress = ttk.Progressbar(analysis_frame, mode='determinate')
        self.progress.pack(fill=tk.X, pady=2)
        
        # 导出功能
        export_frame = ttk.LabelFrame(control_frame, text="导出结果", padding=5)
        export_frame.pack(fill=tk.X)
        
        ttk.Button(export_frame, text="导出Excel", 
                  command=self.export_excel).pack(fill=tk.X, pady=2)
        ttk.Button(export_frame, text="导出JSON", 
                  command=self.export_json).pack(fill=tk.X, pady=2)
    
    def setup_filter_config(self, parent):
        """设置筛选条件配置"""
        filter_frame = ttk.LabelFrame(parent, text="筛选条件设置", padding=10)
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建左右两列的主框架
        main_frame = ttk.Frame(filter_frame)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左列：评分标准设置
        left_frame = ttk.LabelFrame(main_frame, text="📊 评分标准设置", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 右列：一票否决权设置
        right_frame = ttk.LabelFrame(main_frame, text="🚫 一票否决权设置", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 筛选条件输入框
        self.filter_vars = {}
        filter_config = self.config_manager.get_filter_config()
        
        # 左列配置项
        left_items = [
            ("需求供给比_优秀", "需求供给比(优秀)"),
            ("需求供给比_良好", "需求供给比(良好)"),
            ("转化率_优秀", "转化率(优秀)"),
            ("转化率_良好", "转化率(良好)"),
            ("天猫占比_低", "天猫占比(优秀)"),
            ("天猫占比_中", "天猫占比(良好)")
        ]
        
        # 右列配置项
        right_items = [
            ("否决_搜索人气最小值", "搜索人气最小值"),
            ("否决_支付买家数最小值", "支付买家数最小值"), 
            ("否决_需求供给比最小值", "需求供给比最小值"),
            ("否决_天猫占比最大值", "天猫占比最大值(%)")
        ]
        
        # 添加左列项目
        for row, (key, display_name) in enumerate(left_items):
            if key in filter_config:
                ttk.Label(left_frame, text=display_name).grid(
                    row=row, column=0, sticky="w", padx=2, pady=3)
                
                var = tk.DoubleVar(value=filter_config[key])
                self.filter_vars[key] = var
                
                entry = ttk.Entry(left_frame, textvariable=var, width=10)
                entry.grid(row=row, column=1, padx=(10, 2), pady=3)
        
        # 添加右列项目
        for row, (key, display_name) in enumerate(right_items):
            if key in filter_config:
                ttk.Label(right_frame, text=display_name).grid(
                    row=row, column=0, sticky="w", padx=2, pady=3)
                
                var = tk.DoubleVar(value=filter_config[key])
                self.filter_vars[key] = var
                
                entry = ttk.Entry(right_frame, textvariable=var, width=10)
                entry.grid(row=row, column=1, padx=(10, 2), pady=3)
        
        # 重置按钮放在底部中央
        button_frame = ttk.Frame(filter_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="🔄 重置默认值", 
                  command=self.reset_filters).pack()
    
    def setup_result_panel(self, parent):
        """设置结果显示面板"""
        result_frame = ttk.LabelFrame(parent, text="分析结果", padding=10)
        result_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建左右分栏
        paned_window = ttk.PanedWindow(result_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：识别数据
        left_panel = ttk.Frame(paned_window)
        paned_window.add(left_panel, weight=1)

        ttk.Label(left_panel, text="识别数据", font=('TkDefaultFont', 10, 'bold')).pack()

        # 原始数据表格
        raw_columns = ("产品名称", "搜索人气", "支付转化率", "支付买家数", "需求供给比", "天猫占比")
        self.raw_tree = ttk.Treeview(left_panel, columns=raw_columns, show="headings", height=15)

        # 设置原始数据列标题和宽度
        for col in raw_columns:
            self.raw_tree.heading(col, text=col)
            self.raw_tree.column(col, width=80, anchor="center")

        # 原始数据滚动条
        raw_scrollbar = ttk.Scrollbar(left_panel, orient="vertical", command=self.raw_tree.yview)
        self.raw_tree.configure(yscrollcommand=raw_scrollbar.set)

        self.raw_tree.pack(side="left", fill="both", expand=True)
        raw_scrollbar.pack(side="right", fill="y")

        # 右侧：分析结果显示
        right_panel = ttk.Frame(paned_window)
        paned_window.add(right_panel, weight=1)

        ttk.Label(right_panel, text="蓝海分析 (按指数排序)", font=('TkDefaultFont', 10, 'bold')).pack()

        # 分析结果表格
        result_columns = ("产品名称", "蓝海指数", "需求供给比", "转化率", "天猫占比", "评级")
        self.tree = ttk.Treeview(right_panel, columns=result_columns, show="headings", height=15)

        # 设置分析结果列标题和宽度
        for col in result_columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=80, anchor="center")

        # 分析结果滚动条
        scrollbar = ttk.Scrollbar(right_panel, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 详细信息显示
        detail_frame = ttk.LabelFrame(result_frame, text="详细信息", padding=5)
        detail_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.detail_text = tk.Text(detail_frame, height=9, wrap=tk.WORD)
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient="vertical", command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)
        
        self.detail_text.pack(side="left", fill="both", expand=True)
        detail_scrollbar.pack(side="right", fill="y")
        
        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_item_select)
        self.raw_tree.bind('<<TreeviewSelect>>', self.on_raw_item_select)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, padding=(5, 3))
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(0, 5))

    def update_status(self, message: str):
        """更新状态栏信息"""
        self.status_var.set(message)
        self.root.update_idletasks()  # 立即更新界面

    def display_results(self):
        """显示分析结果"""
        self.update_results_display()
    
    def select_folder(self):
        """选择图片文件夹"""
        folder = filedialog.askdirectory(title="选择包含图片的文件夹")
        if folder:
            # 支持的图片格式
            image_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif')
            self.image_files = [
                os.path.join(folder, f) for f in os.listdir(folder)
                if f.lower().endswith(image_extensions)
            ]
            self.current_mode = 'image'
            self.data_files = []  # 清空数据文件列表
            self.file_count_label.config(text=f"已选择 {len(self.image_files)} 个图片文件")
            self.update_status(f"已选择 {len(self.image_files)} 个图片文件，点击'开始分析'进行OCR识别和分析")
    
    def select_files(self):
        """选择单个或多个图片文件"""
        files = filedialog.askopenfilenames(
            title="选择图片文件",
            filetypes=[
                ("图片文件", "*.png *.jpg *.jpeg *.bmp *.tiff *.gif"),
                ("所有文件", "*.*")
            ]
        )
        if files:
            self.image_files = list(files)
            self.current_mode = 'image'
            self.data_files = []  # 清空数据文件列表
            self.file_count_label.config(text=f"已选择 {len(self.image_files)} 个图片文件")
            self.update_status(f"已选择 {len(self.image_files)} 个图片文件，点击'开始分析'进行OCR识别和分析")
    
    def reset_filters(self):
        """重置筛选条件为默认值"""
        filter_config = self.config_manager.get_filter_config()
        for key, var in self.filter_vars.items():
            if key in filter_config:
                var.set(filter_config[key])
    
    def start_analysis(self):
        """开始分析"""
        # 检查是否有文件可分析
        if self.current_mode == 'image' and not self.image_files:
            messagebox.showwarning("警告", "请先选择要分析的图片文件！")
            return
        elif self.current_mode == 'data' and not self.data_files:
            messagebox.showwarning("警告", "请先选择要分析的数据文件！")
            return

        # 更新配置管理器中的筛选条件
        new_config = {}
        for key, var in self.filter_vars.items():
            new_config[key] = var.get()
        self.config_manager.update_filter_config(new_config)

        # 清空之前的结果
        self.results = []
        self.failed_files = []

        # 在新线程中执行分析
        self.analyze_btn.config(state="disabled", text="分析中...")

        if self.current_mode == 'image':
            self.progress.config(maximum=len(self.image_files))
            analysis_thread = threading.Thread(target=self.analyze_images)
        else:
            self.progress.config(maximum=len(self.data_files))
            analysis_thread = threading.Thread(target=self.analyze_data_files)
        analysis_thread.daemon = True
        analysis_thread.start()
    
    def analyze_images(self):
        """分析图片"""
        successful_count = 0
        
        for i, image_path in enumerate(self.image_files):
            try:
                self.status_var.set(f"正在分析: {os.path.basename(image_path)} ({i+1}/{len(self.image_files)})")
                
                # 处理图片
                products_data = self.ocr_processor.process_image(image_path)
                
                if products_data:
                    for product_data in products_data:
                        if product_data and "产品名称" in product_data:
                            # 计算蓝海指数
                            analysis = self.score_calculator.calculate_blue_ocean_score(product_data)
                            self.results.append(analysis)
                    successful_count += 1
                else:
                    self.failed_files.append({
                        'file': image_path,
                        'reason': '未识别到有效产品数据'
                    })
                
                # 更新进度条
                self.progress.config(value=i + 1)
                self.root.update_idletasks()
                
            except Exception as e:
                self.failed_files.append({
                    'file': image_path,
                    'reason': str(e)
                })
                self.logger.error(f"分析图片失败 {image_path}: {e}")
        
        # 分析完成，更新UI
        self.root.after(0, self.analysis_complete)
    
    def analysis_complete(self):
        """分析完成后的UI更新"""
        self.analyze_btn.config(state="normal", text="开始分析")
        self.status_var.set(f"分析完成！成功: {len(self.results)}个产品")
        
        # 更新结果显示
        self.update_results_display()
        
        # 显示摘要
        summary = self.data_exporter.generate_summary_report(self.results)
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(1.0, summary)
    
    def update_results_display(self):
        """更新结果显示"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        for item in self.raw_tree.get_children():
            self.raw_tree.delete(item)

        if not self.results:
            return

        # 左侧显示原始数据 (按识别顺序)
        for result in self.results:
            self.raw_tree.insert("", "end", values=(
                result["产品名称"],
                result.get("搜索人气", "未识别"),
                result.get("支付转化率", "未识别"),
                result.get("支付买家数", "未识别"),
                result.get("需求供给比", "未识别"),
                result.get("天猫占比", "未识别")
            ))

        # 右侧显示分析结果 (按蓝海指数排序)
        sorted_results = sorted(self.results, key=lambda x: x["蓝海指数"], reverse=True)
        for result in sorted_results:
            self.tree.insert("", "end", values=(
                result["产品名称"],
                result["蓝海指数"],
                result.get("需求供给比", "未识别"),
                result.get("支付转化率", "未识别"),
                result.get("天猫占比", "未识别"),
                result["评级"]
            ))
    
    def on_item_select(self, event=None):
        """选择项目时显示详细信息"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            # 现在产品名称在values[0]中
            product_name = item['values'][0] if item['values'] else ''

            # 查找对应的结果
            result = next((r for r in self.results if r.get('产品名称') == product_name), None)
            if result:
                self.show_product_details(result)

    def on_raw_item_select(self, event=None):
        """选择原始数据项目时显示详细信息"""
        selection = self.raw_tree.selection()
        if selection:
            item = self.raw_tree.item(selection[0])
            # 现在产品名称在values[0]中
            product_name = item['values'][0] if item['values'] else ''

            # 查找对应的结果
            result = next((r for r in self.results if r.get('产品名称') == product_name), None)
            if result:
                self.show_product_details(result)
    
    def show_product_details(self, result):
        """显示产品详细信息"""
        # 获取数据
        search_volume = result.get('搜索人气', '-')
        conversion_rate = result.get('支付转化率', '-')
        buyers = result.get('支付买家数', '-')
        supply_demand = result.get('需求供给比', 0)
        tmall_ratio = result.get('天猫占比', '-')

        search_growth = result.get('搜索人气增长', '-')
        conversion_growth = result.get('转化率增长', '-')
        buyers_growth = result.get('买家数增长', '-')
        supply_demand_growth = result.get('需求供给比增长', '-')
        tmall_growth = result.get('天猫占比增长', '-')

        # 格式化表格，使用固定宽度确保对齐
        details = f"""产品名称: {result.get('产品名称', '未知')}
文件名: {result.get('文件名', '未知')}
蓝海指数: {result.get('蓝海指数', 0)}/100
评级: {result.get('评级', '未知')}

基础数据:
    搜索人气      支付转化率    支付买家数     需求供给比      天猫占比
{search_volume:>12}  {conversion_rate:>12}  {buyers:>12}  {supply_demand:>10}  {tmall_ratio:>10}
{search_growth:>12}  {conversion_growth:>12}  {buyers_growth:>12}  {supply_demand_growth:>10}  {tmall_growth:>10}

分析详情:
"""
        
        if ' 分析详情' in result:
            for detail in result['分析详情']:
                details += f"• {detail}\n"
        
        if '否决原因' in result:
            details += f"\n🚫 否决原因: {result['否决原因']}"
        
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(1.0, details)
    
    def export_excel(self):
        """导出Excel文件"""
        if not self.results:
            messagebox.showwarning("警告", "没有分析结果可导出！")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        
        if filename:
            self.data_exporter.export_to_excel(self.results, filename)
    
    def export_json(self):
        """导出JSON文件"""
        if not self.results:
            messagebox.showwarning("警告", "没有分析结果可导出！")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filename:
            self.data_exporter.export_to_json(self.results, filename)

    def select_excel_files(self):
        """选择Excel文件"""
        filenames = filedialog.askopenfilenames(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )

        if filenames:
            self.data_files = list(filenames)
            self.current_mode = 'data'
            self.image_files = []  # 清空图片文件列表
            self.file_count_label.config(text=f"已选择 {len(self.data_files)} 个Excel文件")
            self.update_status(f"已选择 {len(self.data_files)} 个Excel文件，点击'开始分析'进行处理")

    def select_json_files(self):
        """选择JSON文件"""
        filenames = filedialog.askopenfilenames(
            title="选择JSON文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filenames:
            self.data_files = list(filenames)
            self.current_mode = 'data'
            self.image_files = []  # 清空图片文件列表
            self.file_count_label.config(text=f"已选择 {len(self.data_files)} 个JSON文件")
            self.update_status(f"已选择 {len(self.data_files)} 个JSON文件，点击'开始分析'进行处理")

    def select_data_files(self):
        """选择多种数据文件"""
        filenames = filedialog.askopenfilenames(
            title="选择数据文件（支持Excel和JSON）",
            filetypes=[
                ("支持的文件", "*.xlsx *.xls *.json"),
                ("Excel文件", "*.xlsx *.xls"),
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ]
        )

        if filenames:
            self.data_files = list(filenames)
            self.current_mode = 'data'
            self.image_files = []  # 清空图片文件列表

            # 统计文件类型
            excel_count = sum(1 for f in self.data_files if f.lower().endswith(('.xlsx', '.xls')))
            json_count = sum(1 for f in self.data_files if f.lower().endswith('.json'))

            self.file_count_label.config(text=f"已选择 {len(self.data_files)} 个数据文件 (Excel:{excel_count}, JSON:{json_count})")
            self.update_status(f"已选择 {len(self.data_files)} 个数据文件，点击'开始分析'进行处理")

    def analyze_data_files(self):
        """分析数据文件"""
        try:
            self.logger.info(f"开始分析 {len(self.data_files)} 个数据文件")

            # 批量导入数据文件
            self.update_status("正在导入数据文件...")
            all_imported_data = self.data_importer.batch_import_files(self.data_files)

            if not all_imported_data:
                self.update_status("数据导入失败")
                return

            # 合并和去重数据
            self.update_status("正在合并和去重数据...")
            merged_data = self.data_importer.merge_and_deduplicate_data(all_imported_data)

            # 重新分析数据
            self.update_status(f"正在重新分析 {len(merged_data)} 条数据...")

            for i, item in enumerate(merged_data):
                try:
                    # 使用当前配置重新计算蓝海指数
                    result = self.score_calculator.calculate_blue_ocean_score(item)
                    self.results.append(result)

                    # 更新进度
                    progress = int((i + 1) / len(merged_data) * 100)
                    self.progress['value'] = i + 1
                    self.update_status(f"正在分析数据 {i + 1}/{len(merged_data)} ({progress}%)")

                except Exception as e:
                    self.logger.warning(f"分析第 {i+1} 条数据失败: {e}")
                    # 保留原数据，但标记为分析失败
                    failed_item = item.copy()
                    failed_item['蓝海指数'] = 0
                    failed_item['评级'] = '❌ 分析失败'
                    failed_item['分析详情'] = [f"重新分析失败: {str(e)}"]
                    self.results.append(failed_item)
                    self.failed_files.append(f"数据项_{i+1}")

            # 按蓝海指数排序
            self.update_status("正在按蓝海指数排序...")
            self.results = self.data_importer.sort_by_blue_ocean_score(self.results)

            # 更新显示
            self.display_results()

            success_count = len(self.results) - len(self.failed_files)
            self.update_status(f"数据分析完成: 成功 {success_count} 条，失败 {len(self.failed_files)} 条")

            self.logger.info(f"数据文件分析完成: 总计 {len(self.results)} 条数据")

        except Exception as e:
            self.logger.error(f"分析数据文件失败: {e}")
            self.update_status(f"分析失败: {str(e)}")

        finally:
            # 恢复按钮状态
            self.analyze_btn.config(state="normal", text="开始分析")
            self.progress['value'] = 0


    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = BlueOceanAnalyzerV2()
    app.run()
